<?php
/**
 * Plugin Name: Nipperlug New Calculators
 * Description: New separated calculators for ForceGuides
 * Version: 1.0.3
 * Author: ForceGuides Team
 * Text Domain: forceguides-new-calculators
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Auto-deployed via GitHub webhook
// Enqueue common scripts and styles
function fgnew_enqueue_common_assets() {
    // Use time() for cache busting
    $version = time();

    // Common CSS
    wp_enqueue_style(
        'fgnew-common-css',
        plugin_dir_url(__FILE__) . 'css/common-calculator.css',
        array(),
        $version
    );

    // Common JavaScript
    wp_enqueue_script(
        'papaparse',
        'https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js',
        array('jquery'),
        '5.3.0',
        true
    );

    wp_enqueue_script(
        'fgnew-common-js',
        plugin_dir_url(__FILE__) . 'js/calculator-common.js',
        array('jquery', 'papaparse'),
        $version,
        true
    );
}

// Enqueue Chloe calculator assets
function fgnew_chloe_assets() {
    fgnew_enqueue_common_assets();

    // Use time() for cache busting
    $version = time();

    // Enqueue item data first
    wp_enqueue_script(
        'fgnew-item-data',
        plugin_dir_url(__FILE__) . 'js/item-data.js',
        array('jquery'),
        $version,
        true
    );

    // Enqueue Chloe data
    wp_enqueue_script(
        'fgnew-chloe-data',
        plugin_dir_url(__FILE__) . 'js/chloe-data.js',
        array('jquery', 'fgnew-item-data'),
        $version,
        true
    );

    // Chloe CSS
    wp_enqueue_style(
        'fgnew-chloe-css',
        plugin_dir_url(__FILE__) . 'css/chloe-calculator.css',
        array('fgnew-common-css'),
        $version
    );

    // Chloe JavaScript - depends on the data files
    wp_enqueue_script(
        'fgnew-chloe-js',
        plugin_dir_url(__FILE__) . 'js/chloe-calculator.js',
        array('jquery', 'papaparse', 'fgnew-common-js', 'fgnew-item-data', 'fgnew-chloe-data'),
        $version,
        true
    );
}

// Enqueue Devil Shop calculator assets
function fgnew_devil_shop_assets() {
    fgnew_enqueue_common_assets();

    // Use time() for cache busting
    $version = time();

    wp_enqueue_style(
        'fgnew-devil-shop-css',
        plugin_dir_url(__FILE__) . 'css/devil-shop-calculator.css',
        array('fgnew-common-css'),
        $version
    );

    // Enqueue item data first
    wp_enqueue_script(
        'fgnew-item-data',
        plugin_dir_url(__FILE__) . 'js/item-data.js',
        array('jquery'),
        $version,
        true
    );

    // Enqueue Devil Shop data
    wp_enqueue_script(
        'fgnew-devil-shop-data',
        plugin_dir_url(__FILE__) . 'js/devil-shop-data.js',
        array('jquery', 'fgnew-item-data'),
        $version,
        true
    );

    wp_enqueue_script(
        'fgnew-devil-shop-js',
        plugin_dir_url(__FILE__) . 'js/devil-shop-calculator.js',
        array('jquery', 'papaparse', 'fgnew-common-js', 'fgnew-item-data', 'fgnew-devil-shop-data'),
        $version,
        true
    );
}

// Add shortcode for Chloe calculator
function fgnew_chloe_shortcode() {
    fgnew_chloe_assets();

    ob_start();
    include plugin_dir_path(__FILE__) . 'templates/chloe-calculator.html';
    return ob_get_clean();
}
add_shortcode('new_chloe_calc', 'fgnew_chloe_shortcode');

// Add shortcode for Devil Shop calculator
function fgnew_devil_shop_shortcode() {
    fgnew_devil_shop_assets();

    ob_start();
    include plugin_dir_path(__FILE__) . 'templates/devil-shop-calculator.html';
    return ob_get_clean();
}
add_shortcode('new_devil_shop_calc', 'fgnew_devil_shop_shortcode');

// Add shortcode for Amity calculator
function fgnew_amity_shortcode() {
    fgnew_amity_assets();

    ob_start();
    include plugin_dir_path(__FILE__) . 'templates/amity-calculator.html';
    $output = ob_get_clean();

    // Ensure we're not breaking WordPress output
    return '<div class="fgnew-calculator-wrapper">' . $output . '</div>';
}
add_shortcode('new_amity_calc', 'fgnew_amity_shortcode');

// Enqueue Amity calculator assets
function fgnew_amity_assets() {
    fgnew_enqueue_common_assets();

    // Use time() for cache busting
    $version = time();

    // Amity calculator specific CSS
    wp_enqueue_style(
        'fgnew-amity-css',
        plugin_dir_url(__FILE__) . 'css/amity-calculator.css',
        array('fgnew-common-css'),
        $version
    );

    // Amity data - ensure we're using fgnew-chloe-data as the handle for consistency
    wp_enqueue_script(
        'fgnew-chloe-data',
        plugin_dir_url(__FILE__) . 'js/chloe-data.js',
        array('jquery'),
        $version,
        true
    );

    // Amity calculator JavaScript
    wp_enqueue_script(
        'fgnew-amity-js',
        plugin_dir_url(__FILE__) . 'js/amity-calculator.js',
        array('jquery', 'papaparse', 'fgnew-common-js', 'fgnew-chloe-data'),
        $version,
        true
    );
}