/* Devil's Token Shop Calculator Specific Styles */

body {
    background: linear-gradient(135deg, var(--bg), hsl(240 8% 8%));
    color: var(--text);
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.6;
    min-height: 100vh;
  }
  
  /* Token shop table container styling */
  .token-shop-table-container {
    border-radius: 1.25rem;
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    border: 1px solid hsla(240, 6%, 25%, 0.4);
    box-shadow: var(--card-shadow),
                inset 0 1px 1px hsla(0, 0%, 100%, 0.05);
    overflow-x: auto;
    margin: 1.5rem 0;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
  
  /* Token shop table wrapper */
  .token-shop-table-wrapper {
    overflow-x: auto;
  }
  
  /* Token shop table styling */
  .token-shop-table {
    --padding: 1.25rem;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: transparent;
    min-width: 100%;
  }
  
  /* Enhanced header styles */
  .token-shop-table thead {
    background: linear-gradient(100deg, 
      hsla(240, 7%, 20%, 0.9),
      hsla(240, 7%, 18%, 0.8)
    );
    backdrop-filter: blur(16px);
  }
  
  .token-shop-table th {
    padding: 0.9rem 0.6rem;
    font-weight: 600;
    text-align: left;
    color: var(--text);
    letter-spacing: 0.03em;
    position: relative;
    transition: var(--hover-transition);
    white-space: normal;
    word-wrap: break-word;
    hyphens: auto;
  }
  
  /* Header hover effect */
  .token-shop-table th::after {
    content: '';
    position: absolute;
    left: var(--padding);
    right: var(--padding);
    bottom: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    transform: scaleX(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: left;
  }
  
  .token-shop-table th:hover::after {
    transform: scaleX(1);
  }
  
  /* Row styling */
  .token-shop-table tr {
    background: transparent;
    transition: var(--hover-transition);
    line-height: 1.4;
  }
  
  .token-shop-table tr:hover {
    background: hsla(240, 7%, 22%, 0.6);
  }
  
  .token-shop-table td {
    padding: 0.8rem 0.6rem;
    border-bottom: 1px solid var(--row-border);
    transition: var(--hover-transition);
    max-width: 300px;
  }
  
  /* Profit cell styling */
  .profit-per-purchase {
    font-weight: 600;
    transition: var(--hover-transition);
    white-space: nowrap !important;
    overflow: visible !important;
  }
  
  /* Token price configuration */
  .token-price-config {
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--header-bg);
    border: 1px solid var(--header-border);
    border-radius: 6px;
  }
  
  .token-price-config h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.1em;
  }
  
  .token-price-inputs {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .token-price-input {
    display: flex;
    flex-direction: column;
    min-width: 200px;
  }
  
  .token-price-input label {
    margin-bottom: 5px;
    font-weight: bold;
  }
  
  .token-price-input input {
    padding: 6px 10px;
    border: 1px solid var(--border);
    border-radius: 4px;
    background-color: var(--surface);
    color: var(--text);
  }
  
  /* Token shop filter styles */
  #token-shop-filters {
    margin: 2rem 0;
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    padding: 1rem;
    background: var(--glass-bg);
    border-radius: 1rem;
    backdrop-filter: blur(12px);
    border: 1px solid hsla(240, 6%, 25%, 0.4);
  }
  
  #token-shop-filters label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: 0.75rem;
    transition: var(--hover-transition);
    background: hsla(240, 7%, 20%, 0.4);
  }
  
  #token-shop-filters label:hover {
    background: hsla(240, 7%, 25%, 0.6);
    transform: translateY(-1px);
  }
  
  #token-shop-filters input[type="checkbox"] {
    appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--border);
    border-radius: 0.375rem;
    background: var(--surface);
    cursor: pointer;
    position: relative;
    transition: var(--hover-transition);
  }
  
  #token-shop-filters input[type="checkbox"]:checked {
    background: var(--primary);
    border-color: var(--primary);
  }
  
  #token-shop-filters input[type="checkbox"]:checked::before {
    content: '';
    position: absolute;
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(45deg);
    width: 0.25rem;
    height: 0.5rem;
    border: solid white;
    border-width: 0 2px 2px 0;
  }
  
  /* Scrollbar styling */
  .token-shop-table-container::-webkit-scrollbar {
    height: 8px !important;
    background: hsla(240, 8%, 15%, 0.8) !important;
  }
  
  .token-shop-table-container::-webkit-scrollbar-thumb {
    background: hsla(240, 7%, 30%, 0.9) !important;
    border-radius: 4px !important;
    border: 1px solid hsla(240, 7%, 40%, 0.5) !important;
  }
  
  /* Only show scrollbar on hover for a cleaner look */
  .token-shop-table-container:not(:hover)::-webkit-scrollbar {
    opacity: 0 !important;
    height: 4px !important;
  }
  
  /* Firefox scrollbar styling */
  .token-shop-table-container {
    scrollbar-color: hsla(240, 7%, 30%, 0.9) hsla(240, 8%, 15%, 0.8) !important;
    scrollbar-width: thin !important;
  } 