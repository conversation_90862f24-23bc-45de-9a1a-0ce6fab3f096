/**
 * ForceGuides Amity Calculator
 * Helps players find the most efficient path to reach maximum amity
 */

// Add helper functions before AmityCalc definition
function updateResults(message) {
    const resultsContainer = document.getElementById('amity-path-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = `<p class="empty-state">${message}</p>`;
    }
}

function formatNumber(number) {
    return new Intl.NumberFormat('en-US', {
        maximumFractionDigits: 0,
        useGrouping: true
    }).format(Math.round(number));
}

// Inline styles moved to CSS file for better organization

// Amity Calculator module
const AmityCalc = {
    // Initialize data structures
    data: null,

    // Initialize the calculator
    init: function() {
        // Check if ForceCalc is available
        if (typeof ForceCalc === 'undefined') {
            console.error('ForceCalc not available. Make sure calculator-common.js is loaded first.');
            return;
        }

        // Load the calculation logic script
        this.loadCalculationLogic();

        // Set up event listeners
        this.setupEventListeners();

        // Load any saved prices from localStorage
        this.loadSavedPrices();

        // Process the embedded amity data
        this.processAmityData();

        // Set up storage change listener for cross-page updates
        this.setupStorageListener();
    },

    // Load the calculation logic script
    loadCalculationLogic: function() {
        // Check if the script is already loaded
        if (typeof AmityCalcLogic !== 'undefined') {
            console.log('AmityCalcLogic already loaded');
            return;
        }

        // Get the script path
        const scripts = document.getElementsByTagName('script');
        let scriptPath = '';

        for (let i = 0; i < scripts.length; i++) {
            const src = scripts[i].src;
            if (src.indexOf('amity-calculator.js') !== -1) {
                scriptPath = src.substring(0, src.lastIndexOf('/') + 1);
                break;
            }
        }

        if (!scriptPath) {
            console.error('Could not determine script path for AmityCalcLogic');
            return;
        }

        // Create and load the script
        const scriptElement = document.createElement('script');
        scriptElement.src = scriptPath + 'amity-calculator-logic.js';
        scriptElement.async = false; // Make sure it loads synchronously

        scriptElement.onload = function() {
            // Logic loaded successfully
        };

        scriptElement.onerror = function() {
            console.error('Failed to load AmityCalcLogic script');
        };

        document.head.appendChild(scriptElement);
    },

    // Load saved prices from localStorage
    loadSavedPrices: function() {
        // Define helper functions
        this.getIngredientPrice = function(ingredientName) {
            return ForceCalc.utils.getMarketPrice(ingredientName) || 0;
        };

        this.getRecipePrice = function(recipeName) {
            return ForceCalc.utils.getMarketPrice(recipeName) || 0;
        };
    },

    // Process the embedded amity data
    processAmityData: function() {
        console.log('Checking AmityData:', typeof AmityData, AmityData);

        // Check if AmityData exists
        if (typeof AmityData === 'undefined' || !AmityData.items) {
            console.error('Amity data not found');
            document.querySelector('.amity-calculator-container').innerHTML =
                '<div class="error-message">Failed to load amity data. Please refresh the page and try again.</div>';
            return;
        }

        // Use the embedded data
        this.data = AmityData.items;

        // Populate the calculator UI
        this.populateAmityTiers();
    },

    // Parse ALZ value from string (handles formatted numbers like "10.000.000")
    parseAlzValue: function(alzString) {
        if (!alzString) return 0;

        // Remove any non-numeric characters except dots
        const cleanedString = alzString.toString().replace(/[^\d.]/g, '');

        // Replace dots with empty strings (as they're thousand separators)
        const numericString = cleanedString.replace(/\./g, '');

        return parseInt(numericString) || 0;
    },

    // Organize items by amity requirement tiers (simplified)
    organizeItemsByTier: function() {
        this.data.amityTiers = {};

        // Initialize tiers 0-9000 in 1000-point increments
        for (let i = 0; i < 10; i++) {
            this.data.amityTiers[i * 1000] = [];
        }

        // Sort items into appropriate tiers
        this.data.forEach(item => {
            const tierKey = Math.floor((item.requiredAmity || 0) / 1000) * 1000;
            if (this.data.amityTiers[tierKey]) {
                this.data.amityTiers[tierKey].push(item);
            }
        });
    },

    // Populate the amity tiers in the UI
    populateAmityTiers: function() {
        console.log('Data:', this.data);

        // Get container
        const container = document.querySelector('.amity-tiers-container');
        if (!container) {
            console.error('Amity tiers container not found!');
            return;
        }

        // Define tier ranges and names
        const tiers = [
            { min: 0, max: 999, name: "0 - 999 Amity" },
            { min: 1000, max: 1999, name: "1,000 - 1,999 Amity" },
            { min: 2000, max: 2999, name: "2,000 - 2,999 Amity" },
            { min: 3000, max: 3999, name: "3,000 - 3,999 Amity" },
            { min: 4000, max: 4999, name: "4,000 - 4,999 Amity" },
            { min: 5000, max: 5999, name: "5,000 - 5,999 Amity" },
            { min: 6000, max: 6999, name: "6,000 - 6,999 Amity" },
            { min: 7000, max: 7999, name: "7,000 - 7,999 Amity" },
            { min: 8000, max: 8999, name: "8,000 - 8,999 Amity" },
            { min: 9000, max: 10000, name: "9,000 - 10,000 Amity" }
        ];

        // Clear the container first
        container.innerHTML = '';

        // Generate HTML for each tier
        tiers.forEach((tier, index) => {
            const tierItems = this.data.filter(item =>
                item.requiredAmity >= tier.min && item.requiredAmity <= tier.max
            );

            console.log(`Tier ${tier.name}:`, tierItems.length, 'items');

            if (tierItems.length === 0) return;

            // Create tier container
            const tierHTML = `
                <div class="amity-tier" id="amity-tier-${index}">
                    <div class="amity-tier-header${index > 0 ? ' collapsed' : ''}">
                        <h3>${tier.name}</h3>
                        <div class="tier-summary">
                            <span class="toggle-icon">▼</span>
                        </div>
                    </div>
                    <div class="amity-tier-content">
                        <div class="items-grid">
                            ${tierItems.map(item => this.createItemCard(item)).join('')}
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML += tierHTML;
        });

        // Set up event listeners
        this.setupCollapsibleTiers();

        // Add event listeners to the ingredient price inputs
        document.querySelectorAll('.ingredient-price-input').forEach(input => {
            input.addEventListener('input', this.handlePriceInput.bind(this));
        });

        // Add event listeners to the recipe price inputs
        document.querySelectorAll('.recipe-price-input').forEach(input => {
            input.addEventListener('input', this.handleRecipePriceInput.bind(this));
        });
    },

    // Get full icon path for an item (simplified)
    getFullIconPath: function(iconPath) {
        if (!iconPath) return null;

        const isProduction = window.location.hostname.includes('forceguides.com') ||
                           window.location.hostname.includes('hostinger') ||
                           window.location.href.includes('nipperlug');
        const hasWordPress = window.location.href.includes('/wordpress/');

        const basePath = isProduction
            ? '/wp-content/plugins/nipperlug-new-calculators'
            : hasWordPress
                ? '/wordpress/wp-content/plugins/forceguides-new-calculators'
                : '/wp-content/plugins/forceguides-new-calculators';

        return `${basePath}/assets/images_craft_icons/${iconPath}`;
    },

    // Create item card HTML
    createItemCard: function(item) {
        // Generate unique input ID
        const recipeInputId = 'recipe-price-' + item.name.replace(/[^a-z0-9]/gi, '');

        // Get saved recipe price
        const savedRecipePrice = this.getRecipePrice(item.name) || '';
        const formattedRecipePrice = savedRecipePrice ? savedRecipePrice : '';

        // Get icon for this item using the iconPath from data (same as Chloe calculator)
        let itemNameHTML = item.recipe;

        if (item.iconPath) {
            const fullIconPath = this.getFullIconPath(item.iconPath);
            if (fullIconPath) {
                itemNameHTML = `
                    <div class="item-with-icon">
                        <img src="${fullIconPath}" alt="${item.name}" class="item-icon">
                        <span>${item.recipe}</span>
                    </div>
                `;
            }
        }

        // Build HTML for ingredients
        let ingredientsHTML = '';
        if (item.ingredients && item.ingredients.length > 0) {
            ingredientsHTML = `
                <div class="item-ingredients">
                    <div class="item-ingredients-title">Ingredients (Enter Price/Piece)</div>
                    <div class="ingredient-list">
                        ${item.ingredients.map(ingredient => {
                            return this.createIngredientHTML(ingredient);
                        }).join('')}
                    </div>
                </div>
            `;
        }

        return `
            <div class="item-card" data-item-name="${item.name}">
                <div class="item-header">
                    <div class="item-name">${itemNameHTML}</div>
                    <div class="recipe-price-container">
                        <input
                            type="text"
                            id="${recipeInputId}"
                            class="recipe-price-input"
                            placeholder="Sell price (0)"
                            data-recipe-name="${item.name}"
                            value="${formattedRecipePrice}"
                        >
                    </div>
                </div>
                <div class="item-details">
                    <div class="item-detail">
                        <span>Required Amity:</span>
                        <span>${item.requiredAmity || 0}</span>
                    </div>
                    <div class="item-detail">
                        <span>Obtained Amity:</span>
                        <span>${item.obtainedAmity || 0}</span>
                    </div>
                    <div class="item-detail">
                        <span>Register Cost:</span>
                        <span>${item.registerCost ? item.registerCost.toLocaleString() : 0} ALZ</span>
                    </div>
                    <div class="item-detail">
                        <span>Success Rate:</span>
                        <span>${item.successRate || 0}%</span>
                    </div>
                </div>
                ${ingredientsHTML}
            </div>
        `;
    },

    // Create HTML for ingredient items
    createIngredientHTML: function(ingredient) {
        // Get saved price
        const savedPrice = this.getIngredientPrice(ingredient.name) || '';
        const formattedPrice = savedPrice ? savedPrice : '';

        return `
            <div class="ingredient-item">
                <div class="ingredient-name">
                    ${ingredient.name}
                    <span class="ingredient-quantity">×${ingredient.quantity}</span>
                </div>
                <div class="ingredient-price">
                    <input
                        type="text"
                        class="ingredient-price-input"
                        placeholder="Price"
                        value="${formattedPrice}"
                        data-ingredient-name="${ingredient.name}"
                    >
                </div>
            </div>
        `;
    },

    // Setup collapsible tier headers
    setupCollapsibleTiers: function() {
        document.querySelectorAll('.amity-tier-header').forEach(header => {
            header.addEventListener('click', function() {
                this.classList.toggle('collapsed');
            });
        });
    },



    // Setup event listeners for calculator controls
    setupEventListeners: function() {

        // Set up calculate button
        const calculateButton = document.getElementById('calculate-amity-path');
        if (calculateButton) {
            calculateButton.addEventListener('click', this.calculateOptimalPath.bind(this));
        }

        // Set up clear prices button
        const clearButton = document.getElementById('clear-amity-prices');
        if (clearButton) {
            clearButton.addEventListener('click', this.clearPrices.bind(this));
        }

        // Set up search input with debounce
        const searchInput = document.getElementById('amity-search-input');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(() => {
                this.filterItems();
            }, 300));
        }
    },

    // Debounce utility function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Filter items based on search input
    filterItems: function() {
        const searchInput = document.getElementById('amity-search-input');
        if (!searchInput) return;

        const searchQuery = searchInput.value.trim().toLowerCase();

        // If search is empty, show all items
        if (!searchQuery) {
            document.querySelectorAll('.item-card').forEach(card => {
                card.style.display = '';
            });
            return;
        }

        // Normalize search query - remove brackets and extra spaces
        const normalizedQuery = searchQuery.replace(/[\(\)\[\]]/g, ' ').replace(/\s+/g, ' ').trim();
        const searchTerms = normalizedQuery.split(' ');

        document.querySelectorAll('.item-card').forEach(card => {
            const itemName = card.dataset.itemName || '';

            // Get recipe name from the item name display
            const itemNameElement = card.querySelector('.item-name');
            const displayName = itemNameElement ? itemNameElement.textContent.trim() : '';

            // Get ingredient names
            const ingredientElements = card.querySelectorAll('.ingredient-name');
            const ingredientNames = Array.from(ingredientElements).map(el =>
                el.textContent.replace(/×\d+/g, '').trim().toLowerCase()
            );

            // Normalize names for searching
            const normalizedItemName = itemName.toLowerCase().replace(/[\(\)\[\]]/g, ' ').replace(/\s+/g, ' ').trim();
            const normalizedDisplayName = displayName.toLowerCase().replace(/[\(\)\[\]]/g, ' ').replace(/\s+/g, ' ').trim();

            // Check if all search terms match item name or display name
            const itemNameMatch = searchTerms.every(term =>
                normalizedItemName.includes(term) || normalizedDisplayName.includes(term)
            );

            // Check if any ingredient contains all search terms
            const ingredientMatch = ingredientNames.some(name => {
                const normalizedIngredientName = name.replace(/[\(\)\[\]]/g, ' ').replace(/\s+/g, ' ').trim();
                return searchTerms.every(term => normalizedIngredientName.includes(term));
            });

            // Show/hide the card based on match
            const shouldShow = itemNameMatch || ingredientMatch;
            card.style.display = shouldShow ? '' : 'none';
        });
    },

    // Handle ingredient price input
    handlePriceInput: function(event) {
        try {
            const input = event.target;
            const ingredientName = input.dataset.ingredientName;
            const value = parseFloat(input.value) || 0;

            if (ingredientName && value >= 0) {
                // Save the price to shared storage
                ForceCalc.utils.setMarketPrice(ingredientName, value);

                // CRITICAL FIX: Update all other ingredient inputs with the same name on this page
                this.updateAllIngredientPrices(ingredientName, value);

                // CRITICAL FIX: Also update recipe inputs with the same name (cross-synchronization)
                this.updateAllRecipePrices(ingredientName, value);
            }
        } catch (error) {
            console.error('Error updating ingredient price:', error);
        }
    },

    // Handle recipe price input
    handleRecipePriceInput: function(event) {
        try {
            const input = event.target;
            const recipeName = input.dataset.recipeName;
            const value = parseFloat(input.value) || 0;

            if (recipeName && value >= 0) {
                // Save the price to shared storage
                ForceCalc.utils.setMarketPrice(recipeName, value);

                // CRITICAL FIX: Update all other recipe inputs with the same name on this page
                this.updateAllRecipePrices(recipeName, value);

                // CRITICAL FIX: Also update ingredient inputs with the same name (cross-synchronization)
                this.updateAllIngredientPrices(recipeName, value);
            }
        } catch (error) {
            console.error('Error updating recipe price:', error);
        }
    },

    // Update all ingredient price inputs with the same name (internal synchronization)
    updateAllIngredientPrices: function(ingredientName, newPrice) {
        if (!ingredientName) return;

        const normalizedName = ForceCalc.utils.normalizeItemName(ingredientName);

        // Update all ingredient price inputs with matching names
        document.querySelectorAll('.ingredient-price-input').forEach(input => {
            const inputIngredientName = input.dataset.ingredientName;
            if (inputIngredientName &&
                (inputIngredientName === ingredientName ||
                 ForceCalc.utils.normalizeItemName(inputIngredientName) === normalizedName)) {

                // Only update if the value is different to avoid cursor issues
                if (input.value !== newPrice.toString()) {
                    input.value = newPrice || '';
                }
            }
        });
    },

    // Update all recipe price inputs with the same name (internal synchronization)
    updateAllRecipePrices: function(recipeName, newPrice) {
        if (!recipeName) return;

        const normalizedName = ForceCalc.utils.normalizeItemName(recipeName);

        // Update all recipe price inputs with matching names
        document.querySelectorAll('.recipe-price-input').forEach(input => {
            const inputRecipeName = input.dataset.recipeName;
            if (inputRecipeName &&
                (inputRecipeName === recipeName ||
                 ForceCalc.utils.normalizeItemName(inputRecipeName) === normalizedName)) {

                // Only update if the value is different to avoid cursor issues
                if (input.value !== newPrice.toString()) {
                    input.value = newPrice || '';
                }
            }
        });
    },

    // Calculate the optimal amity path
    calculateOptimalPath: function() {
        try {
            // Show loading indicator
            updateResults("Calculating optimal path...");

            // Check if ForceCalc is available
            if (typeof ForceCalc === 'undefined') {
                updateResults("Price sharing system not available. Please refresh the page and try again.");
                return;
            }

            // Check if AmityCalcLogic is available
            if (typeof AmityCalcLogic === 'undefined') {
                this.loadCalculationLogic();

                // Give it a moment to load, then try again
                setTimeout(() => {
                    if (typeof AmityCalcLogic !== 'undefined') {
                        this.calculateOptimalPath();
                    } else {
                        updateResults("Could not load the calculation module. Please refresh the page and try again.");
                    }
                }, 1000);
                return;
            }

            const currentAmityInput = document.getElementById('current-amity');
            const goalAmityInput = document.getElementById('goal-amity');

            if (!currentAmityInput || !goalAmityInput) {
                updateResults("Missing amity input fields");
                return;
            }

            const currentAmity = parseInt(currentAmityInput.value) || 0;
            const goalAmity = parseInt(goalAmityInput.value) || 0;

            if (goalAmity <= currentAmity) {
                updateResults("Goal amity must be greater than current amity");
                return;
            }

            // Limit maximum calculation range to prevent browser hanging
            const maxCalculationRange = 5000;
            if (goalAmity - currentAmity > maxCalculationRange) {
                updateResults(`Calculation range too large. Please limit to ${maxCalculationRange} amity points at a time.`);
                return;
            }

            // Get the prices using the utility functions for consistency
            const calculationPrices = {};
            const recipePrices = {};

            // Collect all ingredient prices from the amity data
            if (this.data && Array.isArray(this.data)) {
                this.data.forEach(item => {
                    // Get ingredient prices
                    if (item.ingredients && Array.isArray(item.ingredients)) {
                        item.ingredients.forEach(ingredient => {
                            const price = ForceCalc.utils.getMarketPrice(ingredient.name);
                            if (price > 0) {
                                calculationPrices[ingredient.name] = price;
                            }
                        });
                    }

                    // Get recipe prices
                    const recipePrice = ForceCalc.utils.getMarketPrice(item.name);
                    if (recipePrice > 0) {
                        recipePrices[item.name] = recipePrice;
                    }
                });
            }

            // Check if we have enough ingredient prices
            const ingredientPricesCount = Object.keys(calculationPrices).filter(
                key => calculationPrices[key] > 0
            ).length;

            if (ingredientPricesCount < 2) {
                updateResults(`Please enter prices for at least 2 ingredients. Currently ${ingredientPricesCount} ingredients have prices.`);
                return;
            }

            // Validate data
            if (!this.data || !Array.isArray(this.data) || this.data.length === 0) {
                updateResults("No amity recipe data available. Please refresh the page.");
                return;
            }

            // Define tier boundaries
            const tierBoundaries = [0, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000];

            // Calculate results for each tier segment
            const results = [];
            let startAmity = currentAmity;
            let endAmity;

            // Find the current tier (safely)
            let currentTierIndex = tierBoundaries.findIndex(boundary => boundary > currentAmity) - 1;
            if (currentTierIndex < 0) {
                // Handle edge case where amity is outside expected range
                currentTierIndex = 0;
            }

            // Safeguard against infinite loops by limiting iterations
            const maxTierIterations = 10;
            let tierIterations = 0;

            for (let i = currentTierIndex; i < tierBoundaries.length - 1 && tierIterations < maxTierIterations; i++) {
                tierIterations++;

                // Calculate end amity for this tier
                endAmity = Math.min(tierBoundaries[i + 1], goalAmity);

                // Skip if we've already reached the goal
                if (startAmity >= endAmity) {
                    continue;
                }

                // Get recipes available at this amity level
                const availableRecipes = AmityCalcLogic.getRecipesForCurrentTier(this.data, startAmity);

                if (!availableRecipes || availableRecipes.length === 0) {
                    results.push({
                        tier: i,
                        startAmity: startAmity,
                        endAmity: endAmity,
                        success: false,
                        message: `No recipes available at amity level ${startAmity}`
                    });
                    break;
                }

                // Calculate optimal path for this tier
                const tierResult = AmityCalcLogic.calculateOptimalPathForTier(
                    availableRecipes,
                    calculationPrices,
                    recipePrices,
                    startAmity,
                    endAmity
                );

                // Add tier information to the result
                tierResult.tier = i;
                tierResult.startAmity = startAmity;
                tierResult.endAmity = endAmity;

                // Check if the tierResult has a path property but not consolidatedPath (old format)
                // If so, create a backward compatibility layer
                if (!tierResult.consolidatedPath && tierResult.path) {
                    // Create a consolidation of the path data
                    const pathGroups = {};

                    tierResult.path.forEach(step => {
                        if (!pathGroups[step.recipe]) {
                            pathGroups[step.recipe] = {
                                recipe: step.recipe,
                                count: 1,
                                costPerCraft: step.cost,
                                totalCraftingCost: step.cost,
                                registerCost: 0, // Can't determine from old format
                                amityGainPerCraft: step.amityGain,
                                totalAmityGain: step.amityGain
                            };
                        } else {
                            pathGroups[step.recipe].count++;
                            pathGroups[step.recipe].totalCraftingCost += step.cost;
                            pathGroups[step.recipe].totalAmityGain += step.amityGain;
                        }
                    });

                    tierResult.consolidatedPath = Object.values(pathGroups);
                }

                results.push(tierResult);

                // If this calculation failed, stop here
                if (!tierResult.success) break;

                // Update start amity for next tier
                startAmity = endAmity;

                // Stop if we've reached the goal
                if (startAmity >= goalAmity) break;
            }

            if (results.length === 0 || !results[0].success) {
                const message = results.length ? results[0].message : "Failed to calculate optimal path";
                updateResults(message);
                return;
            }

            // Display the aggregated results
            this.displayCalculationResults(results, currentAmity, goalAmity);
        } catch (error) {
            console.error('Error in calculateOptimalPath:', error);
            updateResults(`Error during calculation: ${error.message}. Please check console for details.`);
        }
    },

    // Display calculation results
    displayCalculationResults: function(results, currentAmity, goalAmity) {
        const resultsContainer = document.getElementById('amity-path-results');

        if (!resultsContainer) {
            console.error("Results container not found");
            return;
        }

        let html = '';
        let totalCost = 0;
        let totalSteps = 0;

        // Check if we have any valid results
        const hasValidResults = results.some(result => result.success);

        if (!hasValidResults) {
            // Display the specific error message if available
            const errorMessages = results
                .filter(result => !result.success && result.message)
                .map(result => result.message);

            const message = errorMessages.length > 0
                ? errorMessages[0]
                : "No valid path found. Please check ingredient prices.";

            html = `<div class="empty-state">${message}</div>`;

            // Add suggestions for common errors
            html += `
                <div class="calculation-suggestions">
                    <h4>Suggestions:</h4>
                    <ul>
                        <li>Enter prices for all required ingredients</li>
                        <li>Make sure your current amity is correct</li>
                        <li>Try a smaller amity range</li>
                    </ul>
                </div>
            `;
        } else {
            // Process each tier's results
            results.forEach((result) => {
                if (!result.success) return;

                // Add tier header with correct display (endAmity - 1 for display)
                const displayEndAmity = result.endAmity - 1;
                html += `
                    <div class="tier-result">
                        <div class="tier-result-header">
                            <h4>Tier ${result.startAmity} - ${displayEndAmity}</h4>
                        </div>
                `;

                // Add consolidated path steps
                if (result.consolidatedPath && result.consolidatedPath.length > 0) {
                    result.consolidatedPath.forEach((step, stepIndex) => {
                        // Get potential selling price from ForceCalc if available
                        let sellingPrice = 0;
                        if (this.getRecipePrice && step.outputName) {
                            sellingPrice = this.getRecipePrice(step.outputName);
                        } else if (window.ForceCalc && window.ForceCalc.utils && window.ForceCalc.utils.getMarketPrice && step.outputName) {
                            sellingPrice = window.ForceCalc.utils.getMarketPrice(step.outputName);
                        }

                        const totalOutputQuantity = step.outputQuantity * step.count;
                        const potentialRevenue = sellingPrice * totalOutputQuantity;
                        const totalCraftingCost = step.totalCraftingCost;
                        const actualIngredientCost = step.totalActualIngredientCost || totalCraftingCost;
                        const registerCost = step.registerCost;
                        const actualTotalCost = actualIngredientCost + registerCost;
                        const netResult = potentialRevenue - actualTotalCost;

                        // Create a cleaner, more organized display with clear crafting instructions
                        const outputQuantity = step.outputQuantity || 1;
                        const totalItemsProduced = step.count * outputQuantity;

                        // Find the recipe data to get the proper recipe display name
                        const recipeData = this.data.find(item => item.name === step.recipe);
                        const displayRecipeName = recipeData ? recipeData.recipe : step.recipe;

                        html += `
                            <div class="path-step-new">
                                <div class="step-header">
                                    <div class="step-number">${totalSteps + stepIndex + 1}</div>
                                    <div class="step-title">
                                        <div class="craft-instruction">Craft the recipe "${displayRecipeName}" ${step.count} times</div>
                                        <div class="craft-result">→ Produces ${formatNumber(totalItemsProduced)} individual items</div>
                                    </div>
                                    <div class="step-amity">+${formatNumber(step.totalAmityGain)} Amity</div>
                                </div>

                                <div class="step-costs">
                                    <div class="cost-breakdown">
                                        <div class="cost-item">
                                            <span class="cost-label">Ingredient Cost:</span>
                                            <span class="cost-value">${formatNumber(actualIngredientCost)} ALZ</span>
                                        </div>
                                        ${registerCost > 0 ? `
                                            <div class="cost-item">
                                                <span class="cost-label">Register Fee:</span>
                                                <span class="cost-value register-cost">${formatNumber(registerCost)} ALZ</span>
                                            </div>
                                        ` : ''}
                                        <div class="cost-item total-cost">
                                            <span class="cost-label">Total Cost:</span>
                                            <span class="cost-value">${formatNumber(actualTotalCost)} ALZ</span>
                                        </div>
                                    </div>

                                    <div class="production-info">
                                        <div class="cost-item">
                                            <span class="cost-label">Items Produced:</span>
                                            <span class="cost-value">${formatNumber(totalItemsProduced)} items</span>
                                        </div>
                                    </div>

                                    ${sellingPrice > 0 ? `
                                        <div class="optional-selling">
                                            <div class="selling-header">Optional: If you sell these items</div>
                                            <div class="cost-item">
                                                <span class="cost-label">Sell Price Each:</span>
                                                <span class="cost-value">${formatNumber(sellingPrice)} ALZ</span>
                                            </div>
                                            <div class="cost-item">
                                                <span class="cost-label">Revenue from selling:</span>
                                                <span class="cost-value">${formatNumber(potentialRevenue)} ALZ</span>
                                            </div>
                                            <div class="cost-item net-result ${netResult >= 0 ? 'profit' : 'loss'}">
                                                <span class="cost-label">${netResult >= 0 ? 'Net Result:' : 'Net Cost:'}</span>
                                                <span class="cost-value">${netResult >= 0 ? '+' : ''}${formatNumber(netResult)} ALZ</span>
                                            </div>
                                        </div>
                                    ` : `
                                        <div class="no-sell-price">
                                            <div class="sell-price-hint">
                                                💡 Enter a sell price to see potential revenue from selling these items
                                            </div>
                                        </div>
                                    `}
                                </div>
                            </div>
                        `;
                    });

                    // Update totals
                    totalCost += result.totalCost || 0;
                    totalSteps += result.consolidatedPath.length;
                }
                // For backward compatibility with older result format
                else if (result.path && result.path.length > 0) {
                    // Group identical recipes
                    const groupedSteps = {};
                    result.path.forEach(step => {
                        if (!groupedSteps[step.recipe]) {
                            groupedSteps[step.recipe] = {
                                recipe: step.recipe,
                                count: 1,
                                totalCost: step.cost,
                                totalAmityGain: step.amityGain,
                                costPerCraft: step.cost,
                                amityGainPerCraft: step.amityGain
                            };
                        } else {
                            groupedSteps[step.recipe].count++;
                            groupedSteps[step.recipe].totalCost += step.cost;
                            groupedSteps[step.recipe].totalAmityGain += step.amityGain;
                        }
                    });

                    // Display consolidated groups
                    Object.values(groupedSteps).forEach((step, stepIndex) => {
                        // Try to estimate output quantity from recipe name
                        let estimatedOutputQuantity = 1;
                        const quantityMatch = step.recipe.match(/x\s*(\d+)/i);
                        if (quantityMatch && quantityMatch[1]) {
                            estimatedOutputQuantity = parseInt(quantityMatch[1]);
                        }

                        // Extract item name from recipe
                        const itemName = step.recipe.split(' x ')[0].trim();

                        // Get potential selling price using updated methods
                        let sellingPrice = 0;
                        if (this.getRecipePrice) {
                            sellingPrice = this.getRecipePrice(itemName);
                        } else if (window.ForceCalc && window.ForceCalc.utils && window.ForceCalc.utils.getMarketPrice) {
                            sellingPrice = window.ForceCalc.utils.getMarketPrice(itemName);
                        }

                        const totalOutputQuantity = estimatedOutputQuantity * step.count;
                        const potentialRevenue = sellingPrice * totalOutputQuantity;
                        const totalCost = step.totalCost;
                        const netProfit = potentialRevenue - totalCost;

                        // Create display message based on selling price
                        let profitDisplay = 'Enter item sell price';
                        if (sellingPrice > 0) {
                            profitDisplay = `Selling value: ${formatNumber(potentialRevenue)} ALZ`;
                            if (netProfit > 0) {
                                profitDisplay += ` <span class="profit-amount">(Profit: ${formatNumber(netProfit)} ALZ)</span>`;
                            }
                        }

                        html += `
                            <div class="path-step">
                                <div class="step-number">${totalSteps + stepIndex + 1}</div>
                                <div class="step-details">
                                    <div class="step-item">
                                        ${step.count}× ${step.recipe}
                                        <span class="potential-profit">${profitDisplay}</span>
                                    </div>
                                    <div class="step-info">
                                        <span class="step-cost">${formatNumber(step.totalCost)} ALZ</span> |
                                        <span class="step-amity">+${formatNumber(step.totalAmityGain)} Amity</span>
                                    </div>
                                    <div class="step-details-secondary">
                                        <span class="craft-cost">Cost per craft: ${formatNumber(step.costPerCraft)} ALZ</span>
                                        • <span class="amity-per-craft">Amity per craft: ${formatNumber(step.amityGainPerCraft)}</span>
                                        ${totalOutputQuantity > 0 ? `• <span class="total-items">Total items: ${totalOutputQuantity}</span>` : ''}
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    // Update totals
                    totalCost += result.totalCost || 0;
                    totalSteps += Object.keys(groupedSteps).length;
                } else {
                    html += `<div class="empty-tier">No steps found for this tier</div>`;
                }

                html += `</div>`;
            });

            // Calculate comprehensive summary
            const shoppingList = {};
            const sellableItems = {};
            const registerFees = {}; // Track individual register fees
            let totalRawIngredientCost = 0; // Raw ingredient purchase cost
            let totalNetCraftingCost = 0; // Net cost after revenue
            let totalRegisterCost = 0;
            let totalRevenue = 0;
            let totalItemsProduced = 0;

            results.forEach(result => {
                if (result.success && result.consolidatedPath) {
                    result.consolidatedPath.forEach(step => {
                        // Find the recipe in data to get ingredients
                        const recipe = this.data.find(item => item.name === step.outputName);
                        if (recipe && recipe.ingredients) {
                            recipe.ingredients.forEach(ingredient => {
                                const totalNeeded = ingredient.quantity * step.count;
                                const price = ForceCalc.utils.getMarketPrice(ingredient.name) || 0;
                                const ingredientCost = price * totalNeeded;

                                if (shoppingList[ingredient.name]) {
                                    shoppingList[ingredient.name].quantity += totalNeeded;
                                    shoppingList[ingredient.name].totalCost += ingredientCost;
                                } else {
                                    shoppingList[ingredient.name] = {
                                        quantity: totalNeeded,
                                        priceEach: price,
                                        totalCost: ingredientCost
                                    };
                                }

                                // Add to raw ingredient cost
                                totalRawIngredientCost += ingredientCost;
                            });
                        }

                        // Track net crafting cost (after revenue) and register fees
                        totalNetCraftingCost += step.totalActualIngredientCost || step.totalCraftingCost;
                        totalRegisterCost += step.registerCost;

                        // Track individual register fees
                        if (step.registerCost > 0) {
                            const recipeName = step.outputName;
                            if (registerFees[recipeName]) {
                                registerFees[recipeName] += step.registerCost;
                            } else {
                                registerFees[recipeName] = step.registerCost;
                            }
                        }

                        // Track sellable items
                        const sellingPrice = ForceCalc.utils.getMarketPrice(step.outputName) || 0;
                        const itemsProduced = (step.outputQuantity || 1) * step.count;

                        if (sellingPrice > 0) {
                            const itemRevenue = sellingPrice * itemsProduced;
                            totalRevenue += itemRevenue;
                            totalItemsProduced += itemsProduced;

                            // Add to sellable items list
                            if (sellableItems[step.outputName]) {
                                sellableItems[step.outputName].quantity += itemsProduced;
                                sellableItems[step.outputName].totalRevenue += itemRevenue;
                            } else {
                                sellableItems[step.outputName] = {
                                    quantity: itemsProduced,
                                    priceEach: sellingPrice,
                                    totalRevenue: itemRevenue
                                };
                            }
                        }
                    });
                }
            });

            // Calculate totals for display
            const grandTotalNet = totalNetCraftingCost + totalRegisterCost;
            const netResult = totalRevenue - grandTotalNet;

            // Add comprehensive summary
            html += `
                <div class="comprehensive-summary">
                    <div class="summary-section">
                        <h3>📋 Shopping List</h3>
                        <div class="shopping-list">
                            ${Object.keys(shoppingList).length > 0 ?
                                Object.entries(shoppingList).map(([name, item]) => `
                                    <div class="shopping-item">
                                        <span class="item-name">${name}</span>
                                        <span class="item-details">${formatNumber(item.quantity)} × ${formatNumber(item.priceEach)} ALZ = ${formatNumber(item.totalCost)} ALZ</span>
                                    </div>
                                `).join('') :
                                '<div class="no-ingredients">No ingredient prices entered</div>'
                            }
                        </div>
                    </div>

                    <div class="summary-section">
                        <h3>💰 Amity Cost Analysis</h3>
                        <div class="cost-summary">
                            <div class="summary-item">
                                <span class="summary-label">Raw Ingredient Costs:</span>
                                <span class="summary-value">${formatNumber(totalRawIngredientCost)} ALZ</span>
                                <span class="summary-note">(Total cost to buy all ingredients)</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Net Crafting Costs:</span>
                                <span class="summary-value">${formatNumber(totalNetCraftingCost)} ALZ</span>
                                <span class="summary-note">(After selling crafted items)</span>
                            </div>
                            ${totalRegisterCost > 0 ? `
                                <div class="summary-subsection">
                                    <div class="summary-item subsection-header">
                                        <span class="summary-label">Register Fees (One-time):</span>
                                    </div>
                                    ${Object.entries(registerFees).map(([recipeName, cost]) => `
                                        <div class="summary-item register-detail">
                                            <span class="summary-label">• ${recipeName}:</span>
                                            <span class="summary-value register-cost">${formatNumber(cost)} ALZ</span>
                                        </div>
                                    `).join('')}
                                    <div class="summary-item register-total">
                                        <span class="summary-label">Total Register Fees:</span>
                                        <span class="summary-value register-cost">${formatNumber(totalRegisterCost)} ALZ</span>
                                    </div>
                                </div>
                            ` : ''}
                            <div class="summary-item total-line">
                                <span class="summary-label">Total Net Cost for ${formatNumber(goalAmity - currentAmity)} Amity:</span>
                                <span class="summary-value">${formatNumber(grandTotalNet)} ALZ</span>
                            </div>

                            ${totalRevenue > 0 ? `
                                <div class="summary-divider">Optional: If you sell the crafted items</div>
                                <div class="sellable-items-list">
                                    ${Object.entries(sellableItems).map(([itemName, item]) => `
                                        <div class="sellable-item">
                                            <span class="item-name">${itemName}</span>
                                            <span class="item-details">${formatNumber(item.quantity)} × ${formatNumber(item.priceEach)} ALZ = ${formatNumber(item.totalRevenue)} ALZ</span>
                                        </div>
                                    `).join('')}
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">Total Revenue from selling:</span>
                                    <span class="summary-value">${formatNumber(totalRevenue)} ALZ</span>
                                </div>
                                <div class="summary-item net-line ${netResult >= 0 ? 'profit' : 'loss'}">
                                    <span class="summary-label">${netResult >= 0 ? 'Net Result:' : 'Net Cost:'}</span>
                                    <span class="summary-value">${netResult >= 0 ? '+' : ''}${formatNumber(netResult)} ALZ</span>
                                </div>
                            ` : `
                                <div class="no-revenue-note">
                                    💡 Enter sell prices to see potential revenue from selling crafted items
                                </div>
                            `}
                        </div>
                    </div>

                    <div class="summary-section">
                        <h3>🎯 Achievement</h3>
                        <div class="achievement-summary">
                            <div class="summary-item">
                                <span class="summary-label">Amity Gained:</span>
                                <span class="summary-value amity-gain">+${formatNumber(goalAmity - currentAmity)}</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Final Amity:</span>
                                <span class="summary-value">${formatNumber(goalAmity)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        resultsContainer.innerHTML = html;
    },

    // Clear all prices
    clearPrices: function() {
        // Clear ForceCalc prices
        if (window.ForceCalc && window.ForceCalc.data && window.ForceCalc.data.ingredientPrices) {
            window.ForceCalc.data.ingredientPrices.ingredients = {};
            window.ForceCalc.data.ingredientPrices.marketPrices = {};

            if (typeof window.ForceCalc.storage.savePrices === 'function') {
                window.ForceCalc.storage.savePrices();
            }
        }

        // Clear input fields
        document.querySelectorAll('.ingredient-price-input, .recipe-price-input').forEach(input => {
            input.value = '';
        });

        // Clear results
        const resultsContainer = document.getElementById('amity-path-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = '<p class="empty-state">Enter ingredient prices to calculate the optimal path.</p>';
        }


    },

    // Setup storage change listener to catch changes from other calculators
    setupStorageListener: function() {
        window.addEventListener('storage', (event) => {
            if (event.key === 'ingredientPrices') {
                this.refreshAllPriceInputs();
            }
        });
    },

    // Refresh all price inputs with current prices
    refreshAllPriceInputs: function() {
        // Update ingredient price inputs
        document.querySelectorAll('.ingredient-price-input').forEach(input => {
            const ingredientName = input.dataset.ingredientName;
            if (ingredientName) {
                const price = this.getIngredientPrice(ingredientName) || '';
                if (input.value !== price.toString()) {
                    input.value = price || '';
                }
            }
        });

        // Update recipe price inputs
        document.querySelectorAll('.recipe-price-input').forEach(input => {
            const recipeName = input.dataset.recipeName;
            if (recipeName) {
                const price = this.getRecipePrice(recipeName) || '';
                if (input.value !== price.toString()) {
                    input.value = price || '';
                }
            }
        });
    },
};

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    AmityCalc.init();
});