/* Amity Calculator Styles */
.amity-calculator-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    color: var(--text);
    margin: 20px auto;
    max-width: 1200px;
  }

  .amity-calculator-header {
    margin-bottom: 20px;
    text-align: center;
  }

  .amity-calculator-header h2 {
    color: var(--primary-light);
    margin-bottom: 8px;
  }

  .amity-calculator-header p {
    color: var(--text-muted);
    font-size: 16px;
  }

  /* Calculator controls styling */
  .calculator-controls {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
    background: var(--glass-bg);
    padding: 20px;
    border-radius: 1rem;
    border: 1px solid hsla(240, 6%, 25%, 0.4);
    backdrop-filter: blur(16px);
    box-shadow: 0 4px 15px -8px rgba(0, 0, 0, 0.5);
  }

  .amity-inputs {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
  }

  .amity-input-container {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .amity-input-container label {
    font-weight: 500;
    white-space: nowrap;
  }

  .amity-input-container input {
    width: 100px;
    padding: 0.75rem 1rem;
    background: hsla(240, 7%, 20%, 0.4);
    border: 1px solid var(--border);
    border-radius: 0.75rem;
    color: var(--text);
    font-size: 1rem;
    transition: var(--hover-transition);
  }

  .amity-input-container input:focus {
    outline: none;
    border-color: var(--primary);
    background: hsla(240, 7%, 22%, 0.6);
    box-shadow: 0 0 0 2px hsla(210, 90%, 55%, 0.2);
  }

  /* Search container styling */
  .search-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 250px;
  }

  .search-container .search-icon {
    color: var(--text-muted);
    font-size: 1.2rem;
  }

  #amity-search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    background: hsla(240, 7%, 20%, 0.4);
    border: 1px solid var(--border);
    border-radius: 0.75rem;
    color: var(--text);
    font-size: 1rem;
    transition: var(--hover-transition);
  }

  #amity-search-input:focus {
    outline: none;
    border-color: var(--primary);
    background: hsla(240, 7%, 22%, 0.6);
    box-shadow: 0 0 0 2px hsla(210, 90%, 55%, 0.2);
  }

  #amity-search-input::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
  }

  /* Enhanced input field styling - consolidated */
  .amity-calculator-container input[type="number"],
  .amity-calculator-container input[type="text"],
  .recipe-price-input,
  .ingredient-price input {
    background: hsla(240, 7%, 20%, 0.4) !important;
    border: 1px solid var(--border) !important;
    border-radius: 0.75rem !important;
    color: var(--text) !important;
    padding: 0.5rem 0.75rem !important;
    transition: var(--hover-transition) !important;
  }

  .amity-calculator-container input[type="number"]:focus,
  .amity-calculator-container input[type="text"]:focus,
  .recipe-price-input:focus,
  .ingredient-price input:focus {
    border-color: var(--primary) !important;
    background: hsla(240, 7%, 22%, 0.6) !important;
    box-shadow: 0 0 0 2px hsla(210, 90%, 55%, 0.2) !important;
    outline: none !important;
  }

  .amity-calculator-container input[type="number"]:hover,
  .amity-calculator-container input[type="text"]:hover,
  .recipe-price-input:hover,
  .ingredient-price input:hover {
    background: hsla(240, 7%, 25%, 0.6) !important;
  }

  /* Tier styling */
  .amity-tier {
    margin-bottom: 15px;
    border: 1px solid var(--border);
    border-radius: 8px;
    overflow: hidden;
    background: var(--surface);
    box-shadow: var(--card-shadow);
  }

  .amity-tier-header {
    background: var(--header-bg);
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border-bottom: 1px solid var(--header-border);
    transition: var(--hover-transition);
  }

  .amity-tier-header:hover {
    background: var(--menu-hover);
  }

  .amity-tier-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }

  .tier-summary {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .total-cost {
    font-size: 15px;
    color: var(--text-muted);
  }

  .cost-value {
    font-weight: 600;
    color: var(--warning);
  }

  .toggle-icon {
    font-size: 14px;
    transition: transform 0.3s ease;
  }

  .amity-tier-header.collapsed .toggle-icon {
    transform: rotate(-90deg);
  }

  .amity-tier-content {
    padding: 20px;
    background: var(--glass-bg);
    max-height: 600px;
    overflow-y: auto;
    transition: max-height 0.3s ease;
  }

  /* Dark theme scrollbar styling for amity tier content */
  .amity-tier-content::-webkit-scrollbar {
    width: 8px !important;
    background: hsla(240, 8%, 15%, 0.8) !important;
  }

  .amity-tier-content::-webkit-scrollbar-thumb {
    background: hsla(240, 7%, 30%, 0.9) !important;
    border-radius: 4px !important;
    border: 1px solid hsla(240, 7%, 40%, 0.5) !important;
  }

  /* Only show scrollbar on hover for a cleaner look */
  .amity-tier-content:not(:hover)::-webkit-scrollbar {
    opacity: 0 !important;
    width: 4px !important;
  }

  /* Firefox scrollbar styling */
  .amity-tier-content {
    scrollbar-color: hsla(240, 7%, 30%, 0.9) hsla(240, 8%, 15%, 0.8) !important;
    scrollbar-width: thin !important;
  }

  .amity-tier-header.collapsed + .amity-tier-content {
    max-height: 0;
    overflow: hidden;
    padding-top: 0;
    padding-bottom: 0;
  }

  /* Items grid styling */
  .items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
  }

  .item-card {
    background: var(--glass-bg);
    border: 1px solid hsla(240, 6%, 25%, 0.4);
    border-radius: 12px;
    padding: 15px;
    transition: var(--hover-transition);
    display: flex;
    flex-direction: column;
    gap: 12px;
    backdrop-filter: blur(16px);
    box-shadow: 0 4px 15px -8px rgba(0, 0, 0, 0.5);
  }

  .item-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
    border-color: hsla(240, 6%, 30%, 0.6);
    background: hsla(240, 7%, 18%, 0.8);
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
  }

  .item-name {
    font-weight: 600;
    font-size: 14px;
    color: var(--text);
    flex: 1;
  }

  /* Item icon styles */
  .item-with-icon {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .item-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
    flex-shrink: 0;
  }

  .recipe-price-container {
    width: 100px;
  }

  .recipe-price-input {
    width: 100%;
    font-size: 12px;
    text-align: center;
  }

  .recipe-price-input::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
    text-align: center;
    font-size: 11px;
  }

  .item-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 13px;
    color: var(--text-muted);
  }

  .item-detail {
    display: flex;
    justify-content: space-between;
  }

  .item-detail span:last-child {
    font-weight: 500;
    color: var(--text);
  }

  /* Ingredients styling */
  .item-ingredients {
    margin-top: 15px;
    border-top: 1px solid var(--border);
    padding-top: 10px;
  }

  .item-ingredients-title {
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 13px;
    color: var(--primary-light);
  }

  .ingredient-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .ingredient-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 13px;
  }

  .ingredient-name {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .ingredient-quantity {
    margin-left: 5px;
    color: var(--text-muted);
    font-size: 12px;
  }

  .ingredient-price {
    width: 120px;
    display: flex;
    align-items: center;
  }

  .ingredient-price input {
    width: 100%;
    font-size: 12px;
    text-align: center;
  }

  /* Enhanced Buttons */
  .calculate-button, .clear-button {
    background: hsla(240, 7%, 20%, 0.4);
    color: var(--text);
    border: 1px solid var(--border);
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--hover-transition);
    font-size: 1rem;
    backdrop-filter: blur(8px);
  }

  .calculate-button {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: var(--on-primary);
    border-color: var(--primary);
    box-shadow: 0 4px 15px -8px hsla(210, 90%, 55%, 0.4);
  }

  .calculate-button:hover {
    filter: brightness(1.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -8px hsla(210, 90%, 55%, 0.6);
  }

  .clear-button:hover {
    background: hsla(240, 7%, 25%, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px -8px rgba(0, 0, 0, 0.3);
  }

  /* Results section */
  .amity-results {
    margin-top: 30px;
    padding: 25px;
    background: var(--glass-bg);
    border: 1px solid hsla(240, 6%, 25%, 0.4);
    border-radius: 1rem;
    backdrop-filter: blur(16px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .amity-results h3 {
    margin-top: 0;
    color: var(--primary-light);
    margin-bottom: 15px;
    font-size: 18px;
  }

  .empty-state {
    color: var(--text-muted);
    text-align: center;
    font-style: italic;
    padding: 20px;
  }

  /* Path results styling */
  .path-step {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid var(--border);
  }

  .path-step:last-child {
    border-bottom: none;
  }

  .step-number {
    background: #4caf50;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    flex-shrink: 0;
    margin-right: 10px;
  }

  .step-details {
    flex: 1;
  }

  .step-item {
    font-weight: 600;
  }

  .step-cost {
    font-weight: 500;
    color: var(--warning);
  }

  .step-amity {
    font-weight: 500;
    color: var(--success);
  }

  .optimized-summary {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    font-weight: 600;
  }

  .total-amity {
    color: var(--success);
  }

  .total-cost-summary {
    color: var(--warning);
  }

  /* Enhanced styling for cost analysis */
  .summary-subsection {
    margin: 15px 0;
    padding-left: 10px;
    border-left: 3px solid var(--border);
  }

  .subsection-header {
    font-weight: 600;
    color: var(--primary-light);
    margin-bottom: 8px !important;
  }

  .register-detail {
    padding-left: 15px;
    font-size: 0.95em;
    opacity: 0.9;
  }

  .register-total {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid var(--border);
    font-weight: 500;
  }

  .summary-note {
    font-size: 0.85em;
    color: var(--text-muted);
    font-style: italic;
    margin-left: 8px;
  }

  /* Calculation suggestions and results styling */
  .calculation-suggestions {
    margin-top: 15px;
    padding: 10px 15px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
  }

  .calculation-suggestions h4 {
    margin-top: 0;
    color: #ffd700;
  }

  .calculation-suggestions ul {
    margin: 0;
    padding-left: 20px;
  }

  .calculation-suggestions li {
    margin-bottom: 5px;
    color: #e0e0e0;
  }

  .empty-tier {
    padding: 10px;
    color: #aaa;
    font-style: italic;
  }

  /* Enhanced step styling */
  .path-step-new {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
  }

  .step-header {
    background: rgba(255, 255, 255, 0.05);
    padding: 12px 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .step-header .step-number {
    margin-right: 0; /* Override the margin for step-header context */
  }

  .step-title {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .craft-instruction {
    font-weight: 600;
    color: #fff;
    font-size: 15px;
  }

  .craft-result {
    font-weight: 500;
    color: #4caf50;
    font-size: 13px;
  }

  .step-costs {
    padding: 15px;
    display: grid;
    gap: 15px;
  }

  .cost-breakdown, .revenue-breakdown {
    display: grid;
    gap: 8px;
  }

  .cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
  }

  .cost-label {
    color: #ccc;
    font-size: 14px;
  }

  .cost-value {
    color: #fff;
    font-weight: 500;
  }

  .register-cost {
    color: #ff9900 !important;
  }

  .revenue-breakdown {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.2);
    border-radius: 6px;
    padding: 12px;
  }

  .net-result.profit .cost-value {
    color: #4caf50;
    font-weight: bold;
  }

  .net-result.loss .cost-value {
    color: #f44336;
    font-weight: bold;
  }

  .no-sell-price {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.2);
    border-radius: 6px;
    padding: 12px;
  }

  .sell-price-hint {
    color: #ffc107;
    font-size: 13px;
    margin-top: 8px;
    font-style: italic;
  }

  /* Legacy compatibility styles */
  .step-details-secondary {
    margin-top: 5px;
    font-size: 0.85em;
    color: #aaa;
  }

  .potential-profit {
    color: #4caf50;
    font-weight: bold;
    margin-left: auto;
    font-size: 0.9em;
  }

  .profit-amount {
    color: #ffd700;
    font-weight: bold;
  }

  /* Comprehensive summary styles */
  .comprehensive-summary {
    margin-top: 30px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
  }

  .summary-section {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .summary-section:last-child {
    border-bottom: none;
  }

  .summary-section h3 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
  }

  .shopping-list, .cost-summary, .achievement-summary {
    display: grid;
    gap: 8px;
  }

  .shopping-item, .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
  }

  .item-name, .summary-label {
    color: #ccc;
    font-size: 14px;
  }

  .item-details, .summary-value {
    color: #fff;
    font-weight: 500;
    text-align: right;
  }

  .total-line, .net-line {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 12px;
    margin-top: 8px;
    font-weight: 600;
  }

  .net-line.profit .summary-value {
    color: #4caf50;
  }

  .net-line.loss .summary-value {
    color: #f44336;
  }

  .amity-gain {
    color: #4caf50 !important;
    font-weight: bold;
  }

  .no-ingredients, .no-revenue-note {
    color: #ffc107;
    font-style: italic;
    text-align: center;
    padding: 10px;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 6px;
  }