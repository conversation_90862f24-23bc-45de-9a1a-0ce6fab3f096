# DP Profit Calculator - Brainstorming Document

## Project Overview
Based on your existing calculator ecosystem, I've analyzed the patterns and structure of your current calculators (Chloe Craft, Devil Shop, and Amity). Here's a comprehensive brainstorming session for the new **DP Profit Calculator**.

## Current Calculator Architecture Analysis

### Existing Patterns:
1. **Modular Structure**: Each calculator has its own JS, CSS, data, and template files
2. **Shared Infrastructure**: Common utilities in `calculator-common.js` for price management, storage, UI components
3. **Price Synchronization**: All calculators share the same price storage system (`ForceCalc.data.ingredientPrices`)
4. **Dark Theme**: Consistent dark theme with profit color coding (red=loss, orange=low, green=medium/high)
5. **WordPress Integration**: Shortcode system for easy embedding
6. **Local Storage**: Persistent storage for prices, favorites, and settings

### Data Structure Pattern:
- Items have: `name`, `quantity`, `cost` (in DP), `category` (optional)
- Market prices are user-inputted and shared across calculators
- Profit calculation: `(market_price * quantity * (1 - sales_fee)) - (dp_cost * dp_price)`

## DP Profit Calculator Options

### Option 1: Simple DP Shop Calculator (Recommended)
**Similar to Devil Shop but for Dungeon Points**

**Features:**
- Table showing all DP shop items with their DP costs
- User inputs current DP market price (ALZ per DP)
- User inputs market prices for each item
- Calculates profit per purchase: `(item_market_price * quantity * (1 - sales_fee%)) - (dp_cost * dp_market_price)`
- Sortable by profit
- Favorites system
- Search/filter functionality

**Data Structure:**
```javascript
const DPShopData = {
    items: [
        {
            "name": "Force Core (High)",
            "quantity": 50,
            "dpCost": 100,
            "category": "Cores"
        },
        {
            "name": "Upgrade Core (Highest)",
            "quantity": 25,
            "dpCost": 250,
            "category": "Cores"
        }
        // ... more items
    ]
};
```

**Pros:**
- Follows existing patterns exactly
- Easy to implement and maintain
- Integrates seamlessly with current price sharing
- Users can quickly see best DP investments

**Cons:**
- Requires manual data entry for all DP shop items
- Need to research current DP shop inventory

### Option 2: Advanced DP Investment Optimizer
**More sophisticated analysis tool**

**Features:**
- Multiple DP price scenarios (current, predicted, historical)
- Bulk purchase calculator (how many DPs to spend for maximum profit)
- ROI analysis over time
- Market trend indicators
- Investment recommendations based on profit margins

**Additional Data:**
- Historical price data (if available)
- Item rarity/demand indicators
- Seasonal price patterns

**Pros:**
- More valuable for serious players
- Unique selling point vs other calculators
- Could include predictive elements

**Cons:**
- Much more complex to implement
- Requires extensive market data
- May be overkill for most users

### Option 3: DP Efficiency Calculator
**Focus on DP earning vs spending optimization**

**Features:**
- Compare DP earning methods (different dungeons)
- Calculate time investment vs ALZ return
- Include dungeon completion times and DP rewards
- Show optimal dungeon rotation for DP farming

**Data Requirements:**
- Dungeon DP rewards
- Average completion times
- Entry costs/requirements

**Pros:**
- Helps with both earning and spending DP
- More comprehensive approach
- Could be very valuable for planning

**Cons:**
- Requires extensive dungeon data
- More complex user interface
- Harder to maintain with game updates

## Recommended Implementation Plan

### Phase 1: Basic DP Shop Calculator (Option 1)
Start with the simple approach that follows your existing patterns:

1. **Files to Create:**
   - `js/dp-shop-data.js` - DP shop items data
   - `js/dp-shop-calculator.js` - Calculator logic
   - `css/dp-shop-calculator.css` - Styling (minimal, inherit from common)
   - `templates/dp-shop-calculator.html` - HTML template

2. **Key Features:**
   - DP price input (ALZ per DP)
   - Item market price inputs (shared with other calculators)
   - Profit calculation and sorting
   - Favorites and search functionality
   - Sales fee integration

3. **Integration Points:**
   - Add to `forceguides-new-calculators.php`
   - Create shortcode `[new_dp_shop_calc]`
   - Integrate with existing price sharing system

### Phase 2: Enhanced Features (Optional)
If Phase 1 is successful, consider adding:
- Category filtering
- Bulk purchase calculator
- Profit per DP analysis
- Market price suggestions/alerts

## Data Collection Strategy

### DP Shop Items Research:
1. **Manual Game Research**: Log in and document all DP shop items
2. **Community Sources**: Check forums, wikis, Discord for item lists
3. **Player Collaboration**: Ask community to help verify/update data

### Required Data Points:
- Item name (exact in-game spelling)
- DP cost
- Quantity received per purchase
- Item category (for filtering)
- Any purchase limits or restrictions

## Technical Implementation Notes

### Following Existing Patterns:
1. **Namespace**: `ForceCalc.dpShop` (similar to `ForceCalc.tokenShop`)
2. **Price Storage**: Use existing `ForceCalc.utils.getMarketPrice()` and `setMarketPrice()`
3. **UI Components**: Reuse `createClearPricesButton()`, `createSalesFeeInput()`
4. **Styling**: Extend common CSS, minimal custom styles needed

### Key Functions Needed:
```javascript
ForceCalc.dpShop = {
    dpPrice: 0, // ALZ per DP
    calculateProfit: (marketPrice, dpCost, itemQuantity) => {},
    updateAllProfitValues: () => {},
    createDPPriceConfig: () => {},
    createDPShopTable: (dpShopData) => {},
    // ... other methods following existing patterns
};
```

## Next Steps

1. **Confirm Approach**: Which option appeals to you most?
2. **Data Gathering**: Start collecting DP shop item data
3. **Implementation**: Begin with basic structure following Devil Shop patterns
4. **Testing**: Ensure integration with existing price sharing works
5. **Refinement**: Add advanced features based on user feedback

Would you like me to proceed with implementing Option 1 (Simple DP Shop Calculator) or would you prefer to explore one of the other options first?
