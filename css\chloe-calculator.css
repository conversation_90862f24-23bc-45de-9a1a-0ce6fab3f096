/* Chloe Profit Calculator Specific Styles */

body {
  background: linear-gradient(135deg, var(--bg), hsl(240 8% 8%));
  color: var(--text);
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  min-height: 100vh;
}

/* Batch calculation button */
.batch-calc-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
  color: var(--text-light);
  border: none;
  border-radius: 0.5rem;
  padding: 0.3rem 0.6rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: var(--hover-transition);
  white-space: nowrap;
}

.batch-calc-btn:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

/* Batch calculation modal */
.batch-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.batch-modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.batch-modal {
  background: var(--glass-bg);
  border-radius: 1rem;
  border: 1px solid hsla(240, 6%, 25%, 0.4);
  box-shadow: var(--card-shadow);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 1.5rem;
  position: relative;
  transform: translateY(20px);
  transition: transform 0.3s;
}

.batch-modal-overlay.active .batch-modal {
  transform: translateY(0);
}

.batch-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.75rem;
}

.batch-modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-light);
}

.batch-modal-close {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  line-height: 1;
  transition: var(--hover-transition);
}

.batch-modal-close:hover {
  color: var(--text-light);
  transform: scale(1.1);
}

.batch-input-container {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: center;
}

.batch-option-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.batch-checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-muted);
}

.batch-checkbox-label input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  accent-color: var(--primary);
}

.batch-input-container label {
  font-weight: 500;
  min-width: 120px;
}

.batch-input-container input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  background: hsla(240, 7%, 20%, 0.4);
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  color: var(--text);
  font-size: 1rem;
  transition: var(--hover-transition);
}

.batch-input-container input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px hsla(var(--primary-hsl), 0.2);
}

.batch-calculate-btn {
  background: var(--primary-gradient);
  color: var(--text-light);
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--hover-transition);
  margin-left: auto;
  display: block;
}

.batch-calculate-btn:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

.batch-results {
  margin-top: 1.5rem;
  border-top: 1px solid var(--border);
  padding-top: 1rem;
}

.batch-results h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.batch-materials-list {
  margin-bottom: 1.5rem;
}

.batch-material-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--row-border);
}

.batch-material-item:last-child {
  border-bottom: none;
}

.batch-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-top: 1rem;
}

.batch-summary-item {
  background: hsla(240, 7%, 20%, 0.4);
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
}

.batch-summary-item .label {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
}

.batch-summary-item .value {
  font-size: 1.1rem;
  font-weight: 500;
}

.batch-summary-item.profit-high .value {
  color: var(--profit-high);
}

.batch-summary-item.profit-medium .value {
  color: var(--profit-medium);
}

.batch-summary-item.profit-low .value {
  color: var(--profit-low);
}

.batch-summary-item.profit-negative .value {
  color: var(--profit-negative);
}

/* Table container styling */
.table-responsive {
  border-radius: 1.25rem;
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  border: 1px solid hsla(240, 6%, 25%, 0.4);
  box-shadow: var(--card-shadow),
              inset 0 1px 1px hsla(0, 0%, 100%, 0.05);
  overflow-x: auto;
  margin: 1.5rem 0;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Crafting table styling */
.crafting-table {
  --padding: 1.25rem;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: transparent;
  min-width: 100%;
}

/* Enhanced Header Styles */
.crafting-table thead {
  background: linear-gradient(100deg,
    hsla(240, 7%, 20%, 0.9),
    hsla(240, 7%, 18%, 0.8)
  );
  backdrop-filter: blur(16px);
}

.crafting-table th {
  padding: 0.9rem 0.6rem;
  font-weight: 600;
  text-align: left;
  color: var(--text);
  letter-spacing: 0.03em;
  position: relative;
  transition: var(--hover-transition);
  white-space: normal;
  word-wrap: break-word;
  hyphens: auto;
}

/* Improved Header Hover Effect */
.crafting-table th::after {
  content: '';
  position: absolute;
  left: var(--padding);
  right: var(--padding);
  bottom: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  transform: scaleX(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: left;
}

.crafting-table th:hover::after {
  transform: scaleX(1);
}

/* Row styling */
.crafting-table tr.crafting-item-row {
  background: transparent;
  transition: var(--hover-transition);
  cursor: pointer;
  line-height: 1.4;
}

.crafting-table tr.crafting-item-row:hover {
  background: hsla(240, 7%, 22%, 0.6);
  transform: translateX(4px);
}

.crafting-table td {
  padding: 0.8rem 0.6rem;
  border-bottom: 1px solid var(--row-border);
  transition: var(--hover-transition);
  max-width: 300px;
}

/* Recipe Details Row */
.recipe-details-row {
  background: var(--glass-bg);
  transition: var(--hover-transition);
  border-bottom: 1px solid var(--row-border);
  position: relative;
}

.ingredient-details-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.recipe-details-row > td {
  padding: 0.5rem 1rem !important;
  border: none !important;
  text-align: center;
}

.recipe-details-row[style*="display: none"] {
  opacity: 0;
  transform: translateY(-8px);
}

/* Ingredients caption/title */
.ingredient-caption {
  display: block;
  text-align: center;
  margin: 0.5rem auto 0.75rem;
  font-size: 0.9rem;
  color: var(--text-muted);
  max-width: 1200px;
  padding: 0 1rem;
}

.ingredient-caption strong {
  color: var(--primary-light);
  font-weight: 500;
}

/* Enhanced Ingredient Grid */
.ingredient-grid {
  background: hsla(240, 7%, 18%, 0.6) !important;
  border-radius: 12px;
  overflow: hidden;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto !important;
  box-shadow: 0 4px 15px -8px rgba(0, 0, 0, 0.5);
  border: 1px solid hsla(240, 6%, 25%, 0.25);
  position: relative;
}

.ingredient-grid th {
  background-color: hsla(240, 7%, 20%, 0.9) !important;
  color: hsl(0 0% 70%) !important;
  padding: 0.6em 1em !important;
  border-bottom: 1px solid var(--row-border);
  text-align: center !important;
  border-color: hsl(240, 6%, 24%) !important;
  font-size: 0.9em;
}

.ingredient-grid th:first-child {
  text-align: center !important;
  padding-left: 1.25rem !important;
  border-top-left-radius: 10px;
}

.ingredient-grid th:last-child {
  border-top-right-radius: 10px;
}

.ingredient-grid tr:last-child td:first-child {
  border-bottom-left-radius: 10px;
}

.ingredient-grid tr:last-child td:last-child {
  border-bottom-right-radius: 10px;
}

.ingredient-grid td {
  padding: 0.5rem 0.6rem !important;
  border-bottom: 1px solid var(--row-border);
  transition: var(--hover-transition);
  text-align: center !important;
  border-color: hsl(240, 6%, 24%) !important;
}

.ingredient-grid td:first-child {
  text-align: center !important;
  padding-left: 1.25rem !important;
  font-weight: 500;
}

.ingredient-grid .ingredient-total {
  text-align: center;
  font-weight: 500;
}

.ingredient-grid tr:last-child td {
  border-bottom: none;
}

.ingredient-grid tr:hover td {
  background: hsla(240, 7%, 22%, 0.4);
}

.ingredient-grid [contenteditable="true"].ingredient-price {
  padding: 0.6rem 1.2rem;
  min-width: 90px;
  width: auto;
  margin: 0 auto;
  background: hsla(240, 7%, 24%, 0.3);
  border-radius: 6px;
  transition: all 0.2s ease;
  display: inline-block;
  text-align: center;
}

.ingredient-grid [contenteditable="true"].ingredient-price:hover,
.ingredient-grid [contenteditable="true"].ingredient-price:focus {
  background: hsla(240, 7%, 26%, 0.6);
  box-shadow: 0 0 0 2px hsla(var(--primary-hsl), 0.3);
}

/* Search bar styling */
#search-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
  width: 250px;
}

#search-container .search-icon {
  color: var(--text-muted);
  font-size: 1.2rem;
}

#search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: hsla(240, 7%, 20%, 0.4);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  color: var(--text);
  font-size: 1rem;
  transition: var(--hover-transition);
}

#search-input:focus {
  outline: none;
  border-color: var(--primary);
  background: hsla(240, 7%, 22%, 0.6);
  box-shadow: 0 0 0 2px hsla(var(--primary-hsl), 0.2);
}

#search-input::placeholder {
  color: var(--text-muted);
  opacity: 0.7;
}

/* Modern Filter Checkboxes */
#demand-filter {
  margin: 1rem 0 2rem;
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  padding: 1rem;
  background: var(--glass-bg);
  border-radius: 1rem;
  backdrop-filter: blur(12px);
  border: 1px solid hsla(240, 6%, 25%, 0.4);
}

#demand-filter label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 0.75rem;
  transition: var(--hover-transition);
  background: hsla(240, 7%, 20%, 0.4);
}

#demand-filter label:hover {
  background: hsla(240, 7%, 25%, 0.6);
  transform: translateY(-1px);
}

#demand-filter input[type="checkbox"] {
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--border);
  border-radius: 0.375rem;
  background: var(--surface);
  cursor: pointer;
  position: relative;
  transition: var(--hover-transition);
}

#demand-filter input[type="checkbox"]:checked {
  background: var(--primary);
  border-color: var(--primary);
}

#demand-filter input[type="checkbox"]:checked::before {
  content: '';
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 0.25rem;
  height: 0.5rem;
  border: solid white;
  border-width: 0 2px 2px 0;
}

/* Category filter styling */
#category-filter {
  margin: 2rem 0;
  padding: 1.5rem;
  background: var(--glass-bg);
  border-radius: 1rem;
  backdrop-filter: blur(12px);
  border: 1px solid hsla(240, 6%, 25%, 0.4);
}

.filter-content {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 1rem;
}

#category-filter label {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: hsla(240, 7%, 20%, 0.4);
  border-radius: 0.75rem;
  cursor: pointer;
  transition: var(--hover-transition);
  white-space: nowrap;
}

#category-filter label:hover {
  background: hsla(240, 7%, 25%, 0.6);
  transform: translateY(-1px);
}

/* Style for the filter sections title */
.filter-section-title {
  color: var(--text-muted);
  font-size: 0.9em;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

/* Profit cells styling */
.profit-margin, .profit-per-craft {
  font-weight: 600;
  transition: var(--hover-transition);
  white-space: nowrap !important;
  overflow: visible !important;
}

/* Register cost column styling */
.register-cost-column {
  transition: all 0.3s ease;
}

/* Token cost styling */
.token-cost {
  font-weight: 500;
  color: var(--primary-light);
}

/* Warning indicator for missing prices */
.price-warning {
  color: var(--warning);
  margin-left: 0.4rem;
  cursor: help;
  font-size: 1em;
}

/* Scrollbar styling */
.table-responsive::-webkit-scrollbar {
  height: 8px !important;
  background: hsla(240, 8%, 15%, 0.8) !important;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: hsla(240, 7%, 30%, 0.9) !important;
  border-radius: 4px !important;
  border: 1px solid hsla(240, 7%, 40%, 0.5) !important;
}

/* Only show scrollbar on hover for a cleaner look */
.table-responsive:not(:hover)::-webkit-scrollbar {
  opacity: 0 !important;
  height: 4px !important;
}

/* Firefox scrollbar styling */
.table-responsive {
  scrollbar-color: hsla(240, 7%, 30%, 0.9) hsla(240, 8%, 15%, 0.8) !important;
  scrollbar-width: thin !important;
}

/* Item icon styles */
.item-with-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.item-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}