<div class="amity-calculator-container">
    <div class="calculator-controls">
      <div class="amity-inputs">
        <div class="amity-input-container">
          <label for="current-amity">Current Amity:</label>
          <input type="number" id="current-amity" min="0" max="10000" value="0" step="1">
        </div>
        <div class="amity-input-container">
          <label for="goal-amity">Goal Amity:</label>
          <input type="number" id="goal-amity" min="0" max="10000" value="9000" step="1">
        </div>
      </div>
      <div class="search-container">
        <span class="search-icon">🔍</span>
        <input type="text" id="amity-search-input" placeholder="Search recipes or ingredients...">
      </div>
      <button id="calculate-amity-path" class="calculate-button">Calculate Best Path</button>
      <button id="clear-amity-prices" class="clear-button">Clear Prices</button>
    </div>

    <div class="amity-tiers-container">
      <!-- Tier 0-999 -->
      <div class="amity-tier">
        <div class="amity-tier-header" data-tier="0">
          <h3>Amity: 0-999</h3>
          <div class="tier-summary">
            <span class="toggle-icon">▼</span>
          </div>
        </div>
        <div class="amity-tier-content" id="tier-0-content">
          <div class="items-grid">
            <!-- Items will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Tier 1000-1999 -->
      <div class="amity-tier">
        <div class="amity-tier-header" data-tier="1000">
          <h3>Amity: 1000-1999</h3>
          <div class="tier-summary">
            <span class="toggle-icon">▼</span>
          </div>
        </div>
        <div class="amity-tier-content" id="tier-1000-content">
          <div class="items-grid">
            <!-- Items will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Tier 2000-2999 -->
      <div class="amity-tier">
        <div class="amity-tier-header" data-tier="2000">
          <h3>Amity: 2000-2999</h3>
          <div class="tier-summary">
            <span class="toggle-icon">▼</span>
          </div>
        </div>
        <div class="amity-tier-content" id="tier-2000-content">
          <div class="items-grid">
            <!-- Items will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Tier 3000-3999 -->
      <div class="amity-tier">
        <div class="amity-tier-header" data-tier="3000">
          <h3>Amity: 3000-3999</h3>
          <div class="tier-summary">
            <span class="toggle-icon">▼</span>
          </div>
        </div>
        <div class="amity-tier-content" id="tier-3000-content">
          <div class="items-grid">
            <!-- Items will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Tier 4000-4999 -->
      <div class="amity-tier">
        <div class="amity-tier-header" data-tier="4000">
          <h3>Amity: 4000-4999</h3>
          <div class="tier-summary">
            <span class="toggle-icon">▼</span>
          </div>
        </div>
        <div class="amity-tier-content" id="tier-4000-content">
          <div class="items-grid">
            <!-- Items will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Tier 5000-5999 -->
      <div class="amity-tier">
        <div class="amity-tier-header" data-tier="5000">
          <h3>Amity: 5000-5999</h3>
          <div class="tier-summary">
            <span class="toggle-icon">▼</span>
          </div>
        </div>
        <div class="amity-tier-content" id="tier-5000-content">
          <div class="items-grid">
            <!-- Items will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Tier 6000-6999 -->
      <div class="amity-tier">
        <div class="amity-tier-header" data-tier="6000">
          <h3>Amity: 6000-6999</h3>
          <div class="tier-summary">
            <span class="toggle-icon">▼</span>
          </div>
        </div>
        <div class="amity-tier-content" id="tier-6000-content">
          <div class="items-grid">
            <!-- Items will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Tier 7000-7999 -->
      <div class="amity-tier">
        <div class="amity-tier-header" data-tier="7000">
          <h3>Amity: 7000-7999</h3>
          <div class="tier-summary">
            <span class="toggle-icon">▼</span>
          </div>
        </div>
        <div class="amity-tier-content" id="tier-7000-content">
          <div class="items-grid">
            <!-- Items will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Tier 8000-8999 -->
      <div class="amity-tier">
        <div class="amity-tier-header" data-tier="8000">
          <h3>Amity: 8000-8999</h3>
          <div class="tier-summary">
            <span class="toggle-icon">▼</span>
          </div>
        </div>
        <div class="amity-tier-content" id="tier-8000-content">
          <div class="items-grid">
            <!-- Items will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Tier 9000-10000 -->
      <div class="amity-tier">
        <div class="amity-tier-header" data-tier="9000">
          <h3>Amity: 9000-10000</h3>
          <div class="tier-summary">
            <span class="toggle-icon">▼</span>
          </div>
        </div>
        <div class="amity-tier-content" id="tier-9000-content">
          <div class="items-grid">
            <!-- Items will be populated by JavaScript -->
          </div>
        </div>
      </div>
    </div>

    <div class="amity-results">
      <h3>Optimized Amity Path</h3>
      <div id="amity-path-results">
        <!-- Results will be populated by JavaScript -->
        <p class="empty-state">Enter item prices and click "Calculate Best Path" to see results.</p>
      </div>
    </div>
  </div>