/**
 * Amity Calculator Logic
 * Handles optimization calculations for finding the most efficient path to reach target amity
 *
 * IMPORTANT FIX: Failed crafts still give full amity points in the game.
 * Success rate only affects item production (for revenue calculation), not amity gain.
 * This was previously incorrectly reducing amity gain by success rate.
 */

const AmityCalcLogic = {
    /**
     * Calculate the optimal path for a specific tier
     * @param {Array} recipes - Available recipes in the tier
     * @param {Object} prices - Object containing ingredient prices
     * @param {Object} recipeSellingPrices - Object containing recipe selling prices
     * @param {Number} currentAmity - User's current amity
     * @param {Number} targetAmity - Target amity to reach
     * @returns {Object} Optimal path information
     */
    calculateOptimalPathForTier: function(recipes, prices, recipeSellingPrices, currentAmity, targetAmity) {
        // Safety check
        if (!recipes || !Array.isArray(recipes) || recipes.length === 0) {
            console.error('Invalid or empty recipes array provided');
            return {
                success: false,
                message: "No recipes provided for calculation",
                path: []
            };
        }

        if (!prices || typeof prices !== 'object') {
            console.error('Invalid prices object provided');
            return {
                success: false,
                message: "Invalid price data provided",
                path: []
            };
        }

        // Debug info
        console.log(`Calculating optimal path from ${currentAmity} to ${targetAmity}`);
        console.log(`Available recipes: ${recipes.length}`);

        // Calculate efficiency for each recipe
        const recipeEfficiency = recipes.map(recipe => {
            // Calculate total cost of ingredients
            let totalCost = 0;
            const missingIngredients = [];

            if (recipe.ingredients) {
                for (const ingredient of recipe.ingredients) {
                    const ingredientName = ingredient.name;
                    const quantity = ingredient.quantity || 1;
                    const price = prices[ingredientName] || 0;

                    if (price <= 0) {
                        // Track missing ingredients
                        missingIngredients.push(ingredientName);
                    } else {
                        totalCost += price * quantity;
                    }
                }
            }

            // Skip recipes with missing ingredient prices
            if (missingIngredients.length > 0) {
                return {
                    recipe: recipe,
                    efficiency: -1,
                    cost: Infinity,
                    amityGain: 0,
                    missing: true,
                    missingIngredients: missingIngredients
                };
            }

            // Calculate selling price correctly (accounting for output quantity and success rate)
            const sellingPrice = recipeSellingPrices[recipe.name] || 0;
            const outputQuantity = recipe.outputQuantity || 1;
            const actualIngredientCost = totalCost; // Keep track of actual ingredient cost

            // Parse success rate for revenue calculations (failed crafts still give full amity)
            let successRate = 0.8; // Default 80% if not specified

            // Handle different success rate formats
            if (recipe.successRate) {
                if (typeof recipe.successRate === 'number') {
                    if (recipe.successRate > 0 && recipe.successRate <= 1) {
                        // Already in decimal format (0-1)
                        successRate = recipe.successRate;
                    } else if (recipe.successRate > 1 && recipe.successRate <= 100) {
                        // In percentage format (1-100)
                        successRate = recipe.successRate / 100;
                    }
                }
            }

            // Failed crafts still give full amity points, only item production is affected by success rate
            const amityGain = recipe.obtainedAmity;

            // Calculate expected revenue (success rate only affects item production, not amity)
            const expectedItemsProduced = outputQuantity * successRate;
            const totalRevenue = sellingPrice * expectedItemsProduced;
            const netCostPerCraft = Math.max(0, totalCost - totalRevenue);

            const registerCost = recipe.registerCost || 0;

            // Calculate efficiency properly:
            // For profitable recipes (revenue > ingredient cost), we should prioritize them heavily
            // For non-profitable recipes, calculate cost efficiency as before

            let efficiency;

            if (totalRevenue > totalCost) {
                // Recipe is profitable - calculate profit per amity
                const netProfitPerCraft = totalRevenue - totalCost;
                const expectedAmityForTier = Math.min(1000, targetAmity - currentAmity);
                const expectedCrafts = Math.ceil(expectedAmityForTier / amityGain);
                const amortizedRegisterCost = expectedCrafts > 0 ? registerCost / expectedCrafts : 0;
                const totalProfitAfterRegister = netProfitPerCraft - amortizedRegisterCost;

                // For profitable recipes, efficiency = profit per amity (higher is better)
                // Add a large bonus to ensure profitable recipes are always preferred
                efficiency = totalProfitAfterRegister > 0 ?
                    (totalProfitAfterRegister * amityGain) + 1000 : // Large bonus for profitable recipes
                    amityGain / Math.max(1, amortizedRegisterCost); // Fallback if register cost is too high
            } else {
                // Recipe costs money - calculate traditional cost efficiency
                const expectedAmityForTier = Math.min(1000, targetAmity - currentAmity);
                const expectedCrafts = Math.ceil(expectedAmityForTier / amityGain);
                const amortizedRegisterCost = expectedCrafts > 0 ? registerCost / expectedCrafts : 0;
                const totalCostPerCraft = netCostPerCraft + amortizedRegisterCost;

                // For cost recipes, efficiency = amity per ALZ spent (higher is better)
                efficiency = totalCostPerCraft > 0 ? amityGain / totalCostPerCraft : (amityGain > 0 ? Infinity : 0);
            }

            return {
                recipe: recipe,
                efficiency: efficiency,
                costPerCraft: netCostPerCraft,
                actualIngredientCost: actualIngredientCost,
                registerCost: registerCost,
                amityGain: amityGain,
                totalRevenue: totalRevenue,
                outputQuantity: outputQuantity,
                missing: false
            };
        });

        // Sort by efficiency (descending)
        const validRecipes = recipeEfficiency
            .filter(item => !item.missing && item.efficiency > 0)
            .sort((a, b) => b.efficiency - a.efficiency);

        if (validRecipes.length === 0) {
            const missingIngredients = new Set();
            recipeEfficiency.forEach(item => {
                if (item.missing && item.missingIngredients) {
                    item.missingIngredients.forEach(ingredient => missingIngredients.add(ingredient));
                }
            });

            const missingList = Array.from(missingIngredients).join(', ');
            return {
                success: false,
                message: missingList ?
                    `Missing prices for ingredients: ${missingList}` :
                    "No valid recipes found with complete pricing information",
                path: []
            };
        }

        // Calculate the optimal path with quantity consolidation
        const consolidatedPath = [];
        let remainingAmity = targetAmity - currentAmity;
        let totalCost = 0;

        // Always use the most efficient recipe
        const bestRecipe = validRecipes[0];

        // Calculate crafts needed and add the one-time register cost
        const craftsNeeded = Math.ceil(remainingAmity / bestRecipe.amityGain);
        const actualIngredientCostTotal = craftsNeeded * bestRecipe.actualIngredientCost;
        const netCraftingCost = craftsNeeded * bestRecipe.costPerCraft;
        const totalAmityGained = craftsNeeded * bestRecipe.amityGain;

        // Add the register cost only once
        totalCost = netCraftingCost + bestRecipe.registerCost;

        consolidatedPath.push({
            recipe: bestRecipe.recipe.name,
            count: craftsNeeded,
            costPerCraft: bestRecipe.costPerCraft,
            actualIngredientCostPerCraft: bestRecipe.actualIngredientCost,
            totalCraftingCost: netCraftingCost,
            totalActualIngredientCost: actualIngredientCostTotal,
            registerCost: bestRecipe.registerCost,
            amityGainPerCraft: bestRecipe.amityGain,
            totalAmityGain: totalAmityGained,
            outputQuantity: bestRecipe.recipe.outputQuantity || 1,
            outputName: bestRecipe.recipe.name
        });

        return {
            success: true,
            totalCost: totalCost,
            totalSteps: craftsNeeded,
            consolidatedPath: consolidatedPath
        };
    },

    /**
     * Get available recipes for a specific amity tier
     * @param {Array} allRecipes - All recipes from the data
     * @param {Number} currentAmity - Current amity level
     * @returns {Array} Recipes available at the current amity level
     */
    getRecipesForCurrentTier: function(allRecipes, currentAmity) {
        // Safety check
        if (!allRecipes || !Array.isArray(allRecipes)) {
            console.error('Invalid recipes data provided');
            return [];
        }

        // Safety check for out-of-range amity
        if (currentAmity < 0 || currentAmity > 10000) {
            console.warn(`Current amity ${currentAmity} is outside expected ranges`);
            return allRecipes.filter(recipe => (recipe.requiredAmity || 0) <= currentAmity);
        }

        // Simplified tier calculation - each tier is 1000 points
        const currentTierIndex = Math.floor(currentAmity / 1000);
        const getRecipeTier = (recipe) => Math.floor((recipe.requiredAmity || 0) / 1000);
        const givesAmityAtCurrentLevel = (recipe) => getRecipeTier(recipe) === currentTierIndex;

        // Filter recipes by both accessibility and amity-giving capability
        const availableRecipes = allRecipes.filter(recipe => {
            const requiredAmity = recipe.requiredAmity || 0;
            return requiredAmity <= currentAmity && givesAmityAtCurrentLevel(recipe);
        });
        return availableRecipes;
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AmityCalcLogic;
}