/**
 * ForceGuides Calculator - Common Functions
 * Shared logic between different calculator components
 */
const ForceCalc = {
    // Data storage
    data: {
        ingredientPrices: { ingredients: {}, marketPrices: {} },
        favoriteRecipes: new Set(),
        salesFee: 0,
        pendingPriceUpdates: new Map() // Track pending price updates
    },

    // Utility functions
    utils: {
        // Debounce function to limit how often a function can be called
        debounce: (func, wait = 300) => {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Format a number with dots as thousand separators
        formatWithDots: (number) => {
            const num = typeof number === 'number' ? number : parseFloat(number) || 0;
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        },

        // Get time ago string from timestamp
        getTimeAgo: (date) => {
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins}m ago`;
            if (diffHours < 24) return `${diffHours}h ago`;
            if (diffDays < 7) return `${diffDays}d ago`;
            return date.toLocaleDateString();
        },

        // Parse a number from string or element text
        parseNum: (value) => parseFloat(value) || 0,

        // Normalize item names for consistent comparison
        normalizeItemName: (name) => {
            if (!name) return '';

            // First, remove any "x N" suffix (like "x 20")
            const baseNameWithoutQuantity = name.replace(/\s*x\s*\d+$/, '');

            // Then normalize spaces and capitalization
            return baseNameWithoutQuantity.toLowerCase()
                .replace(/\s*\(/, '(')   // Remove spaces before parentheses
                .replace(/\s+/g, ' ')    // Normalize multiple spaces to single space
                .trim();
        },

        // Get market price for an item, checking both normalized and original names
        getMarketPrice: (itemName) => {
            if (!itemName) return 0;

            // First try exact match
            if (ForceCalc.data.ingredientPrices.marketPrices[itemName] !== undefined) {
                return ForceCalc.data.ingredientPrices.marketPrices[itemName];
            }

            // Try normalized match
            const normalizedName = ForceCalc.utils.normalizeItemName(itemName);

            // Check all keys with normalized comparison
            for (const key in ForceCalc.data.ingredientPrices.marketPrices) {
                if (ForceCalc.utils.normalizeItemName(key) === normalizedName) {
                    return ForceCalc.data.ingredientPrices.marketPrices[key];
                }
            }

            // If we get here, no match found in market prices, check ingredient prices
            for (const key in ForceCalc.data.ingredientPrices.ingredients) {
                if (ForceCalc.utils.normalizeItemName(key) === normalizedName) {
                    return ForceCalc.data.ingredientPrices.ingredients[key];
                }
            }

            return 0;
        },

        // Set market price for an item and ensure it's accessible by normalized name
        setMarketPrice: (itemName, price) => {
            if (!itemName) return;

            // Store by original name
            ForceCalc.data.ingredientPrices.marketPrices[itemName] = price;

            // CRITICAL FIX: Also store in ingredients data to ensure full synchronization
            ForceCalc.data.ingredientPrices.ingredients[itemName] = price;

            // Store by normalized name too for better cross-page synchronization
            const normalizedName = ForceCalc.utils.normalizeItemName(itemName);

            // Check for other items with same normalized name and update them all
            for (const key in ForceCalc.data.ingredientPrices.marketPrices) {
                if (ForceCalc.utils.normalizeItemName(key) === normalizedName && key !== itemName) {
                    ForceCalc.data.ingredientPrices.marketPrices[key] = price;
                }
            }

            for (const key in ForceCalc.data.ingredientPrices.ingredients) {
                if (ForceCalc.utils.normalizeItemName(key) === normalizedName && key !== itemName) {
                    ForceCalc.data.ingredientPrices.ingredients[key] = price;
                }
            }

            // Ensure it's saved
            ForceCalc.storage.savePrices();
        },

        // DOM utility functions
        getElement: (row, selector) => row?.querySelector(selector),
        setText: (el, text) => { if (el) el.textContent = text; },

        // Set text content while preserving cursor position for contenteditable elements
        setTextPreserveCursor: (el, text) => {
            if (!el) return;

            // If element is not focused, just set the text content
            if (document.activeElement !== el) {
                el.textContent = text;
                return;
            }

            // If element is focused, preserve cursor position
            const selection = window.getSelection();
            const range = selection.getRangeAt(0);
            const cursorPosition = range.startOffset;

            // Only update if the text is different
            if (el.textContent !== text) {
                el.textContent = text;

                // Restore cursor position
                const newRange = document.createRange();
                newRange.setStart(el.firstChild || el, Math.min(cursorPosition, text.length));
                newRange.collapse(true);
                selection.removeAllRanges();
                selection.addRange(newRange);
            }
        },

        // Update style of profit values based on the amount
        updateProfitStyle: (cell, profit) => {
            cell.classList.remove('profit-negative', 'profit-low', 'profit-medium', 'profit-high');

            if (profit < 0) {
                cell.classList.add('profit-negative');
            } else if (profit < 50000) {
                cell.classList.add('profit-low');
            } else if (profit < 250000) {
                cell.classList.add('profit-medium');
            } else {
                cell.classList.add('profit-high');
            }
        },

        // Return the appropriate CSS class for a profit percentage
        getProfitStatus: (profitPct) => {
            if (profitPct < 0) return 'profit-negative';
            if (profitPct < 5) return 'profit-low';
            if (profitPct < 15) return 'profit-medium';
            return 'profit-high';
        }
    },

    // Local storage management
    storage: {
        // Load prices from localStorage
        loadPrices: () => {
            const saved = localStorage.getItem('ingredientPrices');
            if (saved) {
                ForceCalc.data.ingredientPrices = { ...JSON.parse(saved) };
                ForceCalc.data.ingredientPrices.ingredients = ForceCalc.data.ingredientPrices.ingredients || {};
                ForceCalc.data.ingredientPrices.marketPrices = ForceCalc.data.ingredientPrices.marketPrices || {};
            }
        },

        // Save prices to localStorage
        savePrices: () => {
            localStorage.setItem('ingredientPrices', JSON.stringify(ForceCalc.data.ingredientPrices));
        },

        // Ensure prices are saved (used before page unload)
        ensurePricesSaved: () => {
            ForceCalc.storage.savePrices();
        },

        // Load favorite recipes from localStorage
        loadFavorites: () => {
            const saved = localStorage.getItem('favoriteRecipes');
            if (saved) {
                ForceCalc.data.favoriteRecipes = new Set(JSON.parse(saved));
            }
        },

        // Save favorite recipes to localStorage
        saveFavorites: () => {
            localStorage.setItem('favoriteRecipes', JSON.stringify(Array.from(ForceCalc.data.favoriteRecipes)));
        },

        // Load sales fee from localStorage
        loadSalesFee: () => {
            const saved = localStorage.getItem('salesFee');
            if (saved !== null) {
                // Round to nearest integer
                ForceCalc.data.salesFee = Math.round(parseFloat(saved));
                // Update input fields if they exist
                const feeInputs = document.querySelectorAll('.sales-fee-input');
                feeInputs.forEach(input => {
                    input.value = ForceCalc.data.salesFee;
                });
            }
        },

        // Save sales fee to localStorage
        saveSalesFee: () => {
            localStorage.setItem('salesFee', ForceCalc.data.salesFee.toString());
        },

        // Load filter settings from localStorage
        loadFilterSettings: (prefix = '') => {
            const savedCategoryFilter = localStorage.getItem(`${prefix}categoryFilter`);
            const savedPriceFilter = localStorage.getItem(`${prefix}priceFilter`);
            const savedFavoritesFilter = localStorage.getItem(`${prefix}favoritesFilter`);
            const savedRegisterCostFilter = localStorage.getItem(`${prefix}registerCostFilter`);

            if (savedCategoryFilter) {
                const categoryFilter = document.getElementById(`${prefix}category-filter`);
                if (categoryFilter) {
                    const categories = JSON.parse(savedCategoryFilter);
                    categoryFilter.querySelectorAll('input').forEach(input => {
                        input.checked = categories.includes(input.value);
                    });
                }
            }

            if (savedPriceFilter) {
                const priceFilter = document.getElementById(`${prefix}price-filter`);
                if (priceFilter) priceFilter.checked = JSON.parse(savedPriceFilter);
            }

            if (savedFavoritesFilter) {
                const favoritesFilter = document.getElementById(`${prefix}favorites-filter`);
                if (favoritesFilter) favoritesFilter.checked = JSON.parse(savedFavoritesFilter);
            }

            if (savedRegisterCostFilter) {
                const registerCostFilter = document.getElementById(`${prefix}register-cost-filter`);
                if (registerCostFilter) registerCostFilter.checked = JSON.parse(savedRegisterCostFilter);
            }
        },

        // Save filter settings to localStorage
        saveFilterSettings: (prefix = '') => {
            const categoryFilter = document.getElementById(`${prefix}category-filter`);
            const priceFilter = document.getElementById(`${prefix}price-filter`);
            const favoritesFilter = document.getElementById(`${prefix}favorites-filter`);
            const registerCostFilter = document.getElementById(`${prefix}register-cost-filter`);

            if (categoryFilter) {
                const checkedCategories = Array.from(categoryFilter.querySelectorAll('input:checked'))
                    .map(cb => cb.value);
                localStorage.setItem(`${prefix}categoryFilter`, JSON.stringify(checkedCategories));
            }

            if (priceFilter) {
                localStorage.setItem(`${prefix}priceFilter`, JSON.stringify(priceFilter.checked));
            }

            if (favoritesFilter) {
                localStorage.setItem(`${prefix}favoritesFilter`, JSON.stringify(favoritesFilter.checked));
            }

            if (registerCostFilter) {
                localStorage.setItem(`${prefix}registerCostFilter`, JSON.stringify(registerCostFilter.checked));
            }
        },

        // Clear all prices and update UI
        clearPrices: async () => {
            // Clear price data
            ForceCalc.data.ingredientPrices = { ingredients: {}, marketPrices: {} };
            ForceCalc.data.pendingPriceUpdates.clear();

            // Reload NPC prices to preserve them after clearing
            await ForceCalc.npcPrices.loadNPCPrices();

            ForceCalc.storage.savePrices();

            // Update UI elements - first clear all, then restore NPC prices
            document.querySelectorAll('.ingredient-price, .market-price').forEach(span => {
                ForceCalc.utils.setTextPreserveCursor(span, '0');
            });

            // Restore NPC prices in UI
            ForceCalc.storage.initPrices();

            // Perform a single batch update
            if (typeof ForceCalc.crafting?.recalculateAllRows === 'function') {
                ForceCalc.crafting.recalculateAllRows();
            }

            // Update token shop if it exists
            if (typeof ForceCalc.tokenShop?.updateAllProfitValues === 'function') {
                ForceCalc.tokenShop.updateAllProfitValues();
            }

            // Apply filters as a single batch operation
            if (typeof ForceCalc.crafting?.filterTable === 'function') {
                ForceCalc.crafting.filterTable();
            }
            if (typeof ForceCalc.tokenShop?.filterTable === 'function') {
                ForceCalc.tokenShop.filterTable();
            }
        },

        // Initialize calculators with correct price data
        initPrices: () => {
            // Update market prices in Chloe calculator
            if (document.querySelector('.crafting-table')) {
                // Update recipe prices (market-price spans)
                document.querySelectorAll('.crafting-table .market-price').forEach(priceEl => {
                    const row = priceEl.closest('tr');
                    const itemName = row?.dataset?.itemName;
                    if (itemName) {
                        const price = ForceCalc.utils.getMarketPrice(itemName);
                        if (price) {
                            ForceCalc.utils.setTextPreserveCursor(priceEl, price.toString());
                        }
                    }
                });

                // Update ingredient prices in detailed rows
                document.querySelectorAll('.crafting-table .ingredient-price').forEach(priceEl => {
                    const ingredientName = priceEl.dataset.ingredientName;
                    if (ingredientName) {
                        // Try first as market price, then as ingredient price
                        let price = ForceCalc.utils.getMarketPrice(ingredientName);
                        if (!price) {
                            price = ForceCalc.data.ingredientPrices.ingredients[ingredientName];
                        }

                        if (price) {
                            ForceCalc.utils.setTextPreserveCursor(priceEl, price.toString());

                            // Also update ingredient total
                            const row = priceEl.closest('tr');
                            const quantity = parseInt(priceEl.dataset.quantity) || 0;
                            const totalCell = row.querySelector('.ingredient-total');
                            if (totalCell) {
                                totalCell.textContent = ForceCalc.utils.formatWithDots((quantity * price).toFixed(0));
                            }
                        }
                    }
                });

                // Recalculate all rows to update profits
                if (typeof ForceCalc.crafting?.recalculateAllRows === 'function') {
                    ForceCalc.crafting.recalculateAllRows();
                }
            }

            // Update market prices in Devil Shop calculator
            if (document.querySelector('.token-shop-table')) {
                document.querySelectorAll('.token-shop-table .market-price').forEach(priceEl => {
                    const itemName = priceEl.dataset.itemName;
                    if (itemName) {
                        const price = ForceCalc.utils.getMarketPrice(itemName);
                        if (price) {
                            ForceCalc.utils.setTextPreserveCursor(priceEl, price.toString());
                        }
                    }
                });

                // Update all profit values
                if (typeof ForceCalc.tokenShop?.updateAllProfitValues === 'function') {
                    ForceCalc.tokenShop.updateAllProfitValues();
                }
            }
        }
    },

    // Common UI elements
    ui: {
        // Create sales fee input UI component
        createSalesFeeInput: () => {
            const container = document.createElement('div');
            container.id = 'sales-fee-container';
            container.className = 'sales-fee-container';
            container.innerHTML = `
                <label for="sales-fee-input">Sales Fee (%)</label>
                <input type="number" id="sales-fee-input" class="sales-fee-input" min="0" max="10" step="1" value="${Math.round(ForceCalc.data.salesFee)}">
            `;

            // Add event listener to update calculations when fee changes
            container.querySelector('input').addEventListener('input', (e) => {
                const newValue = parseInt(e.target.value) || 0;
                // Ensure value is between 0-10 and round to nearest integer
                ForceCalc.data.salesFee = Math.max(0, Math.min(10, Math.round(newValue)));
                e.target.value = ForceCalc.data.salesFee;
                ForceCalc.storage.saveSalesFee();

                // Update other sales fee inputs if they exist
                document.querySelectorAll('.sales-fee-input').forEach(input => {
                    if (input !== e.target) {
                        input.value = ForceCalc.data.salesFee;
                    }
                });

                // Update calculations in both calculators if they're on the page
                if (typeof ForceCalc.crafting?.recalculateAllRows === 'function') {
                    ForceCalc.crafting.recalculateAllRows();
                }
                if (typeof ForceCalc.tokenShop?.updateAllProfitValues === 'function') {
                    ForceCalc.tokenShop.updateAllProfitValues();
                }
            });

            return container;
        },

        // Create clear prices button
        createClearPricesButton: () => {
            const clearButton = document.createElement('button');
            clearButton.textContent = 'CLEAR ALL PRICES';
            clearButton.id = 'clear-prices';
            clearButton.type = 'button';
            clearButton.addEventListener('click', ForceCalc.storage.clearPrices);
            return clearButton;
        },

        // Toggle favorite status
        toggleFavorite: (e, itemName) => {
            e.stopPropagation();
            if (ForceCalc.data.favoriteRecipes.has(itemName)) {
                ForceCalc.data.favoriteRecipes.delete(itemName);
                e.target.classList.remove('active');
            } else {
                ForceCalc.data.favoriteRecipes.add(itemName);
                e.target.classList.add('active');
            }
            ForceCalc.storage.saveFavorites();

            // Update filters if they exist
            if (typeof ForceCalc.crafting?.filterTable === 'function') {
                ForceCalc.crafting.filterTable();
            }
            if (typeof ForceCalc.tokenShop?.filterTable === 'function') {
                ForceCalc.tokenShop.filterTable();
            }
        },

        // Create OCR prices button
        createOCRPricesButton: () => {
            const button = document.createElement('button');
            button.id = 'load-ocr-prices-btn';
            button.type = 'button';
            button.className = 'ocr-prices-btn';
            button.onclick = ForceCalc.ocrPrices.loadOCRPrices;

            // Update button text with server name and timestamp
            ForceCalc.ui.updateOCRButtonText(button);

            return button;
        },

        // Update OCR button text with server name and timestamp
        updateOCRButtonText: (button) => {
            const lastUpdate = ForceCalc.ocrPrices.getLastUpdateTime();

            if (lastUpdate) {
                const timeAgo = ForceCalc.utils.getTimeAgo(lastUpdate);
                button.innerHTML = `🎯 Load Latest Prices<br><small>PlayCabal • ${timeAgo}</small>`;
                button.title = `Last updated: ${lastUpdate.toLocaleString()}`;
            } else {
                button.innerHTML = `🎯 Load Latest Prices<br><small>PlayCabal • No data</small>`;
                button.title = 'No price data available yet';
            }
        },

        // Show OCR price dialog
        showOCRPriceDialog: (ocrData) => {
            // Remove any existing dialog
            const existingDialog = document.querySelector('.ocr-price-dialog');
            if (existingDialog) {
                existingDialog.remove();
            }

            const timestamp = new Date(ocrData.timestamp).toLocaleString();
            const priceCount = Object.keys(ocrData.prices).length;

            const dialog = document.createElement('div');
            dialog.className = 'ocr-price-dialog';
            dialog.innerHTML = `
                <div class="ocr-dialog-overlay"></div>
                <div class="ocr-dialog-content">
                    <h3>🎯 Latest Market Prices Available</h3>
                    <p><strong>Updated:</strong> ${timestamp}</p>
                    <p><strong>Items:</strong> ${priceCount} prices found</p>
                    <div class="ocr-price-preview">
                        ${Object.entries(ocrData.prices).slice(0, 5).map(([item, price]) =>
                            `<div class="price-item">${item}: ${ForceCalc.utils.formatWithDots(price)} ALZ</div>`
                        ).join('')}
                        ${priceCount > 5 ? `<div class="price-item">... and ${priceCount - 5} more</div>` : ''}
                    </div>
                    <div class="ocr-dialog-buttons">
                        <button id="load-ocr-prices" class="ocr-btn-primary">Load Prices</button>
                        <button id="cancel-ocr-prices" class="ocr-btn-secondary">Keep Manual Prices</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);

            // Event listeners
            document.getElementById('load-ocr-prices').onclick = () => {
                ForceCalc.ocrPrices.applyOCRPrices(ocrData);
                document.body.removeChild(dialog);
            };

            document.getElementById('cancel-ocr-prices').onclick = () => {
                document.body.removeChild(dialog);
            };

            // Close on overlay click
            dialog.querySelector('.ocr-dialog-overlay').onclick = () => {
                document.body.removeChild(dialog);
            };
        },

        // Show OCR error dialog
        showOCRErrorDialog: () => {
            const dialog = document.createElement('div');
            dialog.className = 'ocr-price-dialog';
            dialog.innerHTML = `
                <div class="ocr-dialog-overlay"></div>
                <div class="ocr-dialog-content">
                    <h3>❌ No Price Data Available</h3>
                    <p>Could not load latest market prices.</p>
                    <p>This could mean:</p>
                    <ul>
                        <li>No recent price updates available</li>
                        <li>Network connection issue</li>
                        <li>Price data file not found</li>
                    </ul>
                    <div class="ocr-dialog-buttons">
                        <button id="close-ocr-error" class="ocr-btn-secondary">OK</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);

            document.getElementById('close-ocr-error').onclick = () => {
                document.body.removeChild(dialog);
            };

            dialog.querySelector('.ocr-dialog-overlay').onclick = () => {
                document.body.removeChild(dialog);
            };
        },

        // Show OCR success message
        showOCRSuccessMessage: (appliedCount, timestamp) => {
            const message = document.createElement('div');
            message.className = 'ocr-success-message';
            message.innerHTML = `
                <div class="success-content">
                    ✅ Successfully loaded ${appliedCount} prices from ${new Date(timestamp).toLocaleString()}
                </div>
            `;

            document.body.appendChild(message);

            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 3000);
        }
    },

    // Shared calculation functions
    calculations: {
        // Calculate crafting metrics with sales fee
        calculateCraftingMetrics: (ingredientCosts, successChance, sellingPrice, craftAmount) => {
            const totalCost = ingredientCosts.reduce((sum, cost) => sum + cost, 0);

            // Apply sales fee to the selling price
            const adjustedSellingPrice = sellingPrice * (1 - ForceCalc.data.salesFee / 100);

            const revenuePerAttempt = (adjustedSellingPrice * craftAmount) * (successChance / 100);
            const profitPerAttempt = revenuePerAttempt - totalCost;

            // Logic for profit margin
            let profitMarginPerAttempt;
            if (totalCost === 0) {
                // If cost is 0 and we have revenue, it's infinite profit margin (show as high)
                profitMarginPerAttempt = revenuePerAttempt > 0 ? 999999 : 0;
            } else {
                profitMarginPerAttempt = (profitPerAttempt / totalCost) * 100;
            }

            return {
                expectedCraftingCost: totalCost,
                profit: profitPerAttempt,
                profitMargin: profitMarginPerAttempt
            };
        }
    },

    // OCR Price Management
    ocrPrices: {
        lastUpdate: null,

        // Fetch OCR prices from GitHub
        fetchOCRPrices: async () => {
            try {
                // Get the current script's URL to determine the plugin path
                const scripts = document.querySelectorAll('script[src*="calculator-common.js"]');
                let pluginUrl = '/wp-content/plugins/forceguides-new-calculators/';

                if (scripts.length > 0) {
                    const scriptSrc = scripts[0].src;
                    const match = scriptSrc.match(/(.*\/forceguides-new-calculators\/)/);
                    if (match) {
                        pluginUrl = match[1];
                    }
                }

                // Try multiple possible paths
                const possiblePaths = [
                    `${pluginUrl}data/prices.json`,
                    '/wp-content/plugins/forceguides-new-calculators/data/prices.json',
                    '/wp-content/plugins/nipperlug-new-calculators/data/prices.json', // Live site path
                    './data/prices.json',
                    'data/prices.json'
                ];

                let response = null;
                let lastError = null;

                for (const path of possiblePaths) {
                    try {
                        console.log(`Trying to fetch prices from: ${path}`);
                        response = await fetch(path);
                        if (response.ok) {
                            console.log(`✅ Successfully fetched from: ${path}`);
                            break;
                        } else {
                            console.log(`❌ Failed to fetch from ${path}: ${response.status} ${response.statusText}`);
                        }
                    } catch (err) {
                        console.log(`❌ Error fetching from ${path}:`, err);
                        lastError = err;
                    }
                }

                if (!response || !response.ok) {
                    throw new Error(`All fetch attempts failed. Last error: ${lastError?.message || 'Unknown error'}`);
                }

                const data = await response.json();
                console.log('✅ Successfully parsed price data:', data);
                return data;
            } catch (error) {
                console.error('❌ Failed to fetch OCR prices:', error);
                return null;
            }
        },

        // Load OCR prices and offer to user
        loadOCRPrices: async () => {
            const ocrData = await ForceCalc.ocrPrices.fetchOCRPrices();
            if (ocrData && ocrData.prices && Object.keys(ocrData.prices).length > 0) {
                ForceCalc.ui.showOCRPriceDialog(ocrData);
            } else {
                ForceCalc.ui.showOCRErrorDialog();
            }
        },

        // Apply OCR prices to calculators
        applyOCRPrices: (priceData) => {
            let appliedCount = 0;

            Object.entries(priceData.prices).forEach(([itemName, price]) => {
                if (price && price > 0) {
                    ForceCalc.utils.setMarketPrice(itemName, price);
                    appliedCount++;
                }
            });

            // Update all calculator displays
            ForceCalc.storage.initPrices();

            // Store timestamp
            ForceCalc.ocrPrices.lastUpdate = priceData.timestamp;
            localStorage.setItem('ocrPricesTimestamp', priceData.timestamp);

            // Update the OCR button text with new timestamp
            const ocrButton = document.getElementById('load-ocr-prices-btn');
            if (ocrButton) {
                ForceCalc.ui.updateOCRButtonText(ocrButton);
            }

            // Show success message
            ForceCalc.ui.showOCRSuccessMessage(appliedCount, priceData.timestamp);
        },

        // Get last update timestamp
        getLastUpdateTime: () => {
            const saved = localStorage.getItem('ocrPricesTimestamp');
            return saved ? new Date(saved) : null;
        }
    },

    // NPC Price Management (fixed prices that load automatically)
    npcPrices: {
        // Load NPC prices automatically on page load
        loadNPCPrices: async () => {
            try {
                // Get the current script's URL to determine the plugin path
                const scripts = document.querySelectorAll('script[src*="calculator-common.js"]');
                let pluginUrl = '/wp-content/plugins/forceguides-new-calculators/';

                if (scripts.length > 0) {
                    const scriptSrc = scripts[0].src;
                    const match = scriptSrc.match(/(.*\/forceguides-new-calculators\/)/);
                    if (match) {
                        pluginUrl = match[1];
                    }
                }

                const response = await fetch(`${pluginUrl}data/npc-prices.json`);
                if (response.ok) {
                    const npcPrices = await response.json();

                    // Apply NPC prices to the price system
                    Object.entries(npcPrices).forEach(([itemName, price]) => {
                        ForceCalc.utils.setMarketPrice(itemName, price);
                    });

                    console.log(`✅ Loaded ${Object.keys(npcPrices).length} NPC prices`);
                }
            } catch (error) {
                console.log('NPC prices file not found or failed to load (this is optional)');
            }
        }
    },

    // Initialization
    init: () => {
        // Load data from localStorage
        ForceCalc.storage.loadPrices();
        ForceCalc.storage.loadFavorites();
        ForceCalc.storage.loadSalesFee();

        // Load NPC prices automatically
        ForceCalc.npcPrices.loadNPCPrices();

        // Set up event listeners for page unload
        window.addEventListener('beforeunload', ForceCalc.storage.ensurePricesSaved);

        // Set up event listeners for price field focus out
        document.addEventListener('focusout', (e) => {
            if (e.target.classList.contains('ingredient-price') ||
                e.target.classList.contains('market-price')) {
                ForceCalc.storage.ensurePricesSaved();
            }
        });

        // Initialize calculators with correct price data
        ForceCalc.storage.initPrices();

        // Update OCR button text every minute to keep "time ago" current
        setInterval(() => {
            const ocrButton = document.getElementById('load-ocr-prices-btn');
            if (ocrButton) {
                ForceCalc.ui.updateOCRButtonText(ocrButton);
            }
        }, 60000); // Update every minute
    },

    debug: {
        // Function to list all prices in the shared storage
        listAllPrices: () => {
            // Debug function removed
        },

        // Get price for a specific item
        getPrice: (itemName) => {
            // Debug function removed
            return ForceCalc.utils.getMarketPrice(itemName);
        }
    }
};

// Initialize common functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Load saved data
    ForceCalc.storage.loadPrices();
    ForceCalc.storage.loadFavorites();
    ForceCalc.storage.loadSalesFee();

    // Initialize prices in all calculators
    ForceCalc.storage.initPrices();

    // Critical fix: Add event listener for localStorage changes from other pages
    window.addEventListener('storage', (e) => {
        // Only proceed if the changed item is our price data
        if (e.key === 'ingredientPrices') {
            // Load the new prices from localStorage
            ForceCalc.storage.loadPrices();

            // Perform a single batch update for each calculator
            if (document.querySelector('.crafting-table')) {
                // Recalculate all rows at once
                if (typeof ForceCalc.crafting?.recalculateAllRows === 'function') {
                    ForceCalc.crafting.recalculateAllRows();
                }
            }

            // Handle Devil Shop price updates
            if (document.querySelector('.token-shop-table')) {
                if (typeof ForceCalc.tokenShop?.updateAllProfitValues === 'function') {
                    ForceCalc.tokenShop.updateAllProfitValues();
                }
            }

            // Handle Amity Calculator price updates
            if (document.querySelector('.amity-calculator-container')) {
                if (typeof AmityCalc?.refreshAllPriceInputs === 'function') {
                    AmityCalc.refreshAllPriceInputs();
                }
            }
        }
    });

    // Save data before page unload
    window.addEventListener('beforeunload', ForceCalc.storage.ensurePricesSaved);
});