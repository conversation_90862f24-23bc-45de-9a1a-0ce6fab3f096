/**
 * ForceGuides Devil Shop Calculator - Data
 * This file contains the token shop data for the Devil Shop Profit Calculator
 */

const DevilShopData = {
    // Items that can be purchased with Devil's Tokens
    items: [
        {
            "name": "Material Core(Osmium)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 1
        },
        {
            "name": "Material Core(Red Osmium)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 1
        },
        {
            "name": "Material Core(SIGMetal)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 2
        },
        {
            "name": "Material Core(Mithril)",
            "quantity": 127,
            "tokenType": "Highest",
            "tokensRequired": 1
        },
        {
            "name": "Material Core(Archridium)",
            "quantity": 127,
            "tokenType": "Highest",
            "tokensRequired": 3
        },
        {
            "name": "Material Core(Palladium)",
            "quantity": 127,
            "tokenType": "Highest",
            "tokensRequired": 5
        },
        {
            "name": "Quartz Core(Lapis)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 1
        },
        {
            "name": "Quartz Core(Topaz)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 2
        },
        {
            "name": "Quartz Core(SIGMetal)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 3
        },
        {
            "name": "Quartz Core(Mithril)",
            "quantity": 127,
            "tokenType": "Highest",
            "tokensRequired": 1
        },
        {
            "name": "Quartz Core(Archridium)",
            "quantity": 127,
            "tokenType": "Highest",
            "tokensRequired": 3
        },
        {
            "name": "Quartz Core(Palladium)",
            "quantity": 127,
            "tokenType": "Highest",
            "tokensRequired": 5
        },
        {
            "name": "Astral Core(Lapis)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 1
        },
        {
            "name": "Astral Core(Topaz)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 1
        },
        {
            "name": "Astral Core(SIGMetal)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 1
        },
        {
            "name": "Astral Core(Mithril)",
            "quantity": 127,
            "tokenType": "Highest",
            "tokensRequired": 1
        },
        {
            "name": "Astral Core(Archridium)",
            "quantity": 127,
            "tokenType": "Highest",
            "tokensRequired": 1
        },
        {
            "name": "Astral Core(Palladium)",
            "quantity": 127,
            "tokenType": "Highest",
            "tokensRequired": 1
        },
        {
            "name": "Slot Extender(Highest)",
            "quantity": 1,
            "tokenType": "Highest",
            "tokensRequired": 2000
        },
        {
            "name": "Slot Extender(High)",
            "quantity": 1,
            "tokenType": "High",
            "tokensRequired": 500
        },
        {
            "name": "Upgrade Core(Crystal)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 10
        },
        {
            "name": "Force Core(Crystal)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 10
        },
        {
            "name": "Upgrade Core(Piece)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 1
        },
        {
            "name": "Force Core(Piece)",
            "quantity": 127,
            "tokenType": "High",
            "tokensRequired": 1
        }
    ]
}; 