/**
 * ForceGuides Item Data
 * This file contains shared item data used across calculators
 */

const ItemData = {
    // Stack sizes for different item types
    stackSizes: {
        // Default stack size for most items
        default: 127,
        
        // Items with stack size 999
        "Force Essence": 999,
        "Upgrade Core Set": 999,
        
        // Non-stackable items (stack size 1)
        "Force Core(Low)": 1,
        "Force Core(Medium)": 1,
        "Force Core(High)": 1,
        "Force Core(Highest)": 1,
        "Force Core(Ultimate)": 1,
        "Upgrade Core(Low)": 1,
        "Upgrade Core(Medium)": 1,
        "Upgrade Core(High)": 1,
        "Upgrade Core(Highest)": 1,
        "Upgrade Core(Ultimate)": 1,
        "Slot Extender": 1,
        
        // Items with stack size 127
        "Force Core(Piece)": 127,
        "Force Core(Crystal)": 127,
        "Material Core": 127,
        "Upgrade Core(Crystal)": 127,
        "Quartz Core": 127,
        "Shape Cartridge": 127,
        "Disc": 127
    },
    
    // Get stack size for an item
    getStackSize: function(itemName) {
        if (!itemName) return this.stackSizes.default;
        
        // Normalize the item name for comparison
        const normalizedName = itemName.toLowerCase();
        
        // Check exact matches first
        for (const [key, size] of Object.entries(this.stackSizes)) {
            if (key !== 'default' && normalizedName === key.toLowerCase()) {
                return size;
            }
        }
        
        // Check partial matches
        for (const [key, size] of Object.entries(this.stackSizes)) {
            if (key !== 'default' && normalizedName.includes(key.toLowerCase())) {
                return size;
            }
        }
        
        // Return default stack size if no match found
        return this.stackSizes.default;
    }
};
