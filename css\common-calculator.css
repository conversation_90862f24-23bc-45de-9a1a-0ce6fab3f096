/* Common CSS Variables and Base Styles */
:root {
    --bg: hsl(240 8% 12%);
    --surface: hsl(240 7% 16%);
    --surface-elevated: hsl(240 7% 20%);
    --primary: hsl(210 90% 55%);
    --primary-light: hsl(210 90% 65%);
    --on-primary: hsl(0 0% 100%);
    --secondary: hsl(260 70% 65%);
    --success: hsl(145 80% 42%);
    --warning: hsl(35 100% 55%);
    --error: hsl(0 85% 55%);
    --text: hsl(0 0% 95%);
    --text-muted: hsl(0 0% 70%);
    --border: hsl(240 6% 20%) !important;
    --row-border: hsl(240, 6%, 15%) !important;

    /* Additional Variables */
    --card-shadow: 0 8px 32px hsl(0 0% 0% / 0.3);
    --hover-transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    --glass-bg: hsla(240, 7%, 16%, 0.7);
    --header-bg: hsla(240, 7%, 20%, 0.9);
    --header-border: hsla(240, 6%, 15%, 0.8) !important;
    --header-shadow: 0 8px 32px hsl(0 0% 0% / 0.3);
    --menu-hover: hsla(240, 7%, 25%, 0.6);
}

/* Calculator common controls */
.calculator-controls {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: flex-start;
}

/* Clear button styling */
#clear-prices {
    background: var(--surface-elevated);
    color: var(--text);
    border: 1px solid var(--border);
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.02em;
    transition: var(--hover-transition);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    box-shadow: var(--card-shadow);
}

#clear-prices:hover {
    background: var(--menu-hover);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px hsla(0, 0%, 0%, 0.3);
}

#clear-prices:active {
    transform: translateY(0);
    opacity: 0.9;
}

/* Sales fee input styling */
.sales-fee-container {
    display: inline-flex;
    align-items: center;
    background: var(--surface-elevated);
    padding: 0.5rem 1rem;
    border-radius: 0.75rem;
    border: 1px solid var(--border);
    box-shadow: var(--card-shadow);
}

.sales-fee-container label {
    font-weight: 500;
    color: var(--text);
    margin-right: 8px;
    white-space: nowrap;
}

.sales-fee-container input {
    width: 60px;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border);
    border-radius: 0.75rem;
    background: hsla(240, 7%, 20%, 0.4);
    color: var(--text);
    transition: var(--hover-transition);
}

.sales-fee-container input:focus {
    outline: none;
    border-color: var(--primary);
    background: hsla(240, 7%, 22%, 0.6);
    box-shadow: 0 0 0 2px hsla(var(--primary-hsl), 0.2);
}

.sales-fee-container input::-webkit-inner-spin-button,
.sales-fee-container input::-webkit-outer-spin-button {
    opacity: 1;
}

/* Enhanced Profit Status Colors */
.profit-negative {
    color: var(--error) !important;
    text-shadow: 0 0 20px hsla(0, 85%, 55%, 0.3);
}

.profit-low {
    color: var(--warning) !important;
    text-shadow: 0 0 20px hsla(35, 100%, 55%, 0.3);
}

.profit-medium {
    color: var(--success) !important;
    text-shadow: 0 0 20px hsla(145, 80%, 42%, 0.3);
}

.profit-high {
    color: var(--success) !important;
    text-shadow: 0 0 20px hsla(145, 80%, 42%, 0.4);
}

/* Enhanced Editable Fields */
[contenteditable="true"] {
    background: hsla(240, 7%, 18%, 0.8) !important;
    border: 1px solid var(--border) !important;
    border-radius: 0.75rem;
    padding: 0.75rem 1.25rem;
    color: var(--text);
    transition: var(--hover-transition);
    min-width: 100px;
    outline: none;
    text-align: right;
}

[contenteditable="true"]:hover {
    background: hsla(240, 7%, 22%, 0.9) !important;
}

[contenteditable="true"]:focus {
    border-color: var(--primary) !important;
    background: var(--surface-elevated) !important;
}

/* Favorite button styling */
.favorite-btn {
    cursor: pointer;
    color: #ccc;
    user-select: none;
    font-size: 1.2em;
    transition: color 0.2s;
}

.favorite-btn:hover {
    color: #ff6b6b;
}

.favorite-btn.active {
    color: #ff4444;
}

.favorite-btn:active {
    transform: scale(0.95);
}

/* Tooltip styling */
.info-icon {
    cursor: help;
    margin-left: 0.5rem;
    opacity: 0.7;
    transition: var(--hover-transition);
}

.info-icon:hover {
    opacity: 1;
}

/* Error message styling */
.error-message {
    padding: 15px;
    background-color: hsla(0, 70%, 50%, 0.1);
    border-left: 4px solid var(--error);
    color: var(--text);
    margin: 15px 0;
    border-radius: 4px;
}

/* Enhance table borders */
.table-responsive {
    border: 1px solid var(--border) !important;
}

.crafting-table td,
.token-shop-table td {
    border-bottom: 1px solid var(--row-border) !important;
}

/* Ingredient grid border enhancements */
.ingredient-grid {
    border: 1px solid var(--border) !important;
    background: hsla(240, 7%, 14%, 0.6) !important;
}

.ingredient-grid th,
.ingredient-grid td {
    border-color: var(--row-border) !important;
}

/* Fix dark mode calculator borders */
.chloe-profit-calculator table,
.devil-shop-profit-calculator table {
    border-collapse: separate !important;
}

/* Override all table borders with !important */
.crafting-table,
.crafting-table th,
.crafting-table td,
.token-shop-table,
.token-shop-table th,
.token-shop-table td,
.ingredient-grid,
.ingredient-grid th,
.ingredient-grid td {
    border: 1px solid #1c1c24 !important;
}

/* Remove double borders */
.crafting-table th,
.token-shop-table th {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
}

.crafting-table td,
.token-shop-table td {
    border-left: none !important;
    border-right: none !important;
}

/* Special treatment for ingredient grid */
.ingredient-grid {
    border: 1px solid #1c1c24 !important;
    background-color: #18181f !important;
}

.ingredient-grid th {
    background-color: #18181f !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
}

.ingredient-grid td {
    border-left: none !important;
    border-right: none !important;
}

/* Fix table container */
.table-responsive,
.token-shop-table-container {
    border: 1px solid #1c1c24 !important;
    background-color: #1a1a21 !important;
}

/* Force table background colors */
.crafting-table thead,
.token-shop-table thead {
    background-color: #18181f !important;
}

/* Force recipe details row background */
.recipe-details-row {
    background-color: #18181f !important;
}

/* Fix borders and colors to match original calculator style */
.crafting-table,
.crafting-table th,
.crafting-table td,
.token-shop-table,
.token-shop-table th,
.token-shop-table td {
    /* Remove default WordPress table borders */
    border: none !important;
}

/* Table container styling */
.table-responsive,
.token-shop-table-container {
    border-radius: 1.25rem !important;
    background: hsla(240, 7%, 16%, 0.7) !important;
    backdrop-filter: blur(16px) !important;
    border: 1px solid hsla(240, 6%, 25%, 0.4) !important;
    box-shadow: 0 8px 32px hsl(0 0% 0% / 0.3),
                inset 0 1px 1px hsla(0, 0%, 100%, 0.05) !important;
    overflow-x: auto !important;
    margin: 1.5rem 0 !important;
}

/* Table header styling */
.crafting-table thead,
.token-shop-table thead {
    background: linear-gradient(100deg,
        hsla(240, 7%, 20%, 0.9),
        hsla(240, 7%, 18%, 0.8)
    ) !important;
    backdrop-filter: blur(16px) !important;
}

/* Table cell bottom borders only */
.crafting-table td,
.token-shop-table td {
    border-bottom: 1px solid hsl(240, 6%, 24%) !important;
    background: transparent !important;
}

/* Row hover effect */
.crafting-table tr.crafting-item-row:hover,
.token-shop-table tr:hover {
    background: hsla(240, 7%, 22%, 0.6) !important;
    transform: translateX(4px) !important;
}

/* Recipe details row styling */
.recipe-details-row {
    background: hsla(240, 15%, 9%, 0.95) !important;
    border-top: 2px solid hsla(240, 7%, 25%, 0.8) !important;
    border-bottom: 2px solid hsla(240, 7%, 25%, 0.8) !important;
}

.recipe-details-row > td {
    padding: 0 !important;
    border: none !important;
}

/* Ingredient grid styling */
.ingredient-grid {
    background-color: hsla(240, 7%, 18%, 0.6) !important;
    border: none !important;
    margin: 0.5rem 0 !important;
}

.ingredient-grid th {
    background-color: hsla(240, 7%, 20%, 0.9) !important;
    color: hsl(0 0% 70%) !important;
    border-bottom: 1px solid hsl(240, 6%, 24%) !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
}

.ingredient-grid td {
    border-bottom: 1px solid hsl(240, 6%, 24%) !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
}

.ingredient-grid tr:last-child td {
    border-bottom: none !important;
}

/* Editable fields styling */
[contenteditable="true"] {
    background: hsla(240, 7%, 22%, 0.6) !important;
    border: 1px solid hsl(240 6% 30%) !important;
    border-radius: 0.75rem !important;
    padding: 0.5rem 0.75rem !important;
}

[contenteditable="true"]:hover {
    background: hsla(240, 7%, 25%, 0.8) !important;
}

[contenteditable="true"]:focus {
    border-color: hsl(210 90% 55%) !important;
    box-shadow: 0 0 0 3px hsla(210, 90%, 55%, 0.2) !important;
    background: hsl(240 7% 20%) !important;
}

/* Profit text colors */
.profit-negative { color: hsl(0 85% 55%) !important; }
.profit-low { color: hsl(35 100% 55%) !important; }
.profit-medium, .profit-high { color: hsl(145 80% 42%) !important; }

/* OCR Prices Button */
.ocr-prices-btn {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: var(--on-primary);
    border: 1px solid var(--primary);
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.02em;
    transition: var(--hover-transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    box-shadow: var(--card-shadow);
}

.ocr-prices-btn:hover {
    background: linear-gradient(135deg, var(--primary-light), var(--primary));
    transform: translateY(-2px);
    box-shadow: 0 8px 24px hsla(210, 90%, 55%, 0.3);
}

.ocr-prices-btn:active {
    transform: translateY(0);
    opacity: 0.9;
}

/* OCR Price Dialog */
.ocr-price-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ocr-dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: hsla(0, 0%, 0%, 0.7);
    backdrop-filter: blur(4px);
}

.ocr-dialog-content {
    position: relative;
    background: var(--surface-elevated);
    border: 1px solid var(--border);
    border-radius: 1rem;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    box-shadow: var(--card-shadow);
    color: var(--text);
}

.ocr-dialog-content h3 {
    margin: 0 0 1rem 0;
    color: var(--primary);
    font-size: 1.25rem;
}

.ocr-dialog-content p {
    margin: 0.5rem 0;
    color: var(--text-muted);
}

.ocr-price-preview {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
    max-height: 200px;
    overflow-y: auto;
}

.price-item {
    padding: 0.25rem 0;
    font-family: monospace;
    font-size: 0.9rem;
    color: var(--text);
}

.ocr-dialog-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    justify-content: flex-end;
}

.ocr-btn-primary {
    background: var(--primary);
    color: var(--on-primary);
    border: 1px solid var(--primary);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--hover-transition);
}

.ocr-btn-primary:hover {
    background: var(--primary-light);
    transform: translateY(-1px);
}

.ocr-btn-secondary {
    background: var(--surface);
    color: var(--text);
    border: 1px solid var(--border);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--hover-transition);
}

.ocr-btn-secondary:hover {
    background: var(--surface-elevated);
    transform: translateY(-1px);
}

/* OCR Success Message */
.ocr-success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10001;
    background: var(--success);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--card-shadow);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.success-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

/* Fixed Price Styling */
.fixed-price {
    background: var(--surface-elevated) !important;
    color: var(--primary) !important;
    border: 1px solid var(--primary) !important;
    cursor: not-allowed !important;
    font-weight: 600 !important;
}

.fixed-price:hover {
    background: var(--surface-elevated) !important;
}