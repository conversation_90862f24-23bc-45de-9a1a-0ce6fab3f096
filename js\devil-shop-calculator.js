/**
 * Devil Shop Profit Calculator
 * Calculator for Devil's Token shop profit
 */
document.addEventListener('DOMContentLoaded', () => {
    // Initialize ForceCalc.tokenShop namespace
    ForceCalc.tokenShop = {
        tokenPrices: {
            "High": 0,
            "Highest": 0
        },

        // Load token prices from localStorage
        loadTokenPrices: () => {
            const saved = localStorage.getItem('tokenPrices');
            if (saved) {
                ForceCalc.tokenShop.tokenPrices = JSON.parse(saved);
            }
        },

        // Save token prices to localStorage
        saveTokenPrices: () => {
            localStorage.setItem('tokenPrices', JSON.stringify(ForceCalc.tokenShop.tokenPrices));
        },

        // Calculate profit for token shop items
        calculateProfit: (marketPrice, tokenCost, itemQuantity, tokenType) => {
            const tokenPrice = ForceCalc.tokenShop.tokenPrices[tokenType] || 0;

            // Apply sales fee to the market price
            const adjustedMarketPrice = marketPrice * (1 - ForceCalc.data.salesFee / 100);

            const totalMarketValue = adjustedMarketPrice * itemQuantity;
            const totalTokenCost = tokenCost * tokenPrice;
            return totalMarketValue - totalTokenCost;
        },

        // Create token price configuration UI
        createTokenPriceConfig: () => {
            const configHTML = `
                <div class="token-price-config">
                    <h3>Token Market Prices</h3>
                    <div class="token-price-inputs">
                        <div class="token-price-input">
                            <label>Devil's Token (High)</label>
                            <input type="number" id="high-token-price" value="${ForceCalc.tokenShop.tokenPrices.High}" min="0">
                        </div>
                        <div class="token-price-input">
                            <label>Devil's Token (Highest)</label>
                            <input type="number" id="highest-token-price" value="${ForceCalc.tokenShop.tokenPrices.Highest}" min="0">
                        </div>
                    </div>
                </div>
            `;

            const placeholder = document.getElementById('token-calculator-placeholder');
            if (placeholder) {
                placeholder.insertAdjacentHTML('afterbegin', configHTML);

                // Add event listeners for token price changes
                document.getElementById('high-token-price').addEventListener('input', (e) => {
                    ForceCalc.tokenShop.tokenPrices.High = parseFloat(e.target.value) || 0;
                    ForceCalc.tokenShop.saveTokenPrices();
                    ForceCalc.tokenShop.updateAllProfitValues();
                });

                document.getElementById('highest-token-price').addEventListener('input', (e) => {
                    ForceCalc.tokenShop.tokenPrices.Highest = parseFloat(e.target.value) || 0;
                    ForceCalc.tokenShop.saveTokenPrices();
                    ForceCalc.tokenShop.updateAllProfitValues();
                });
            }
        },

        // Update all profit values in the table
        updateAllProfitValues: () => {
            document.querySelectorAll('.token-shop-table tbody tr').forEach(row => {
                // Correct column indices - the first column is the favorite button
                const itemName = row.querySelector('td:nth-child(2)').textContent; // Second column
                const itemQuantity = parseInt(row.querySelector('td:nth-child(3)').textContent) || 1; // Third column
                const tokenType = row.querySelector('td:nth-child(4)').textContent; // Fourth column
                const tokenRequired = parseInt(row.querySelector('td:nth-child(5)').textContent) || 0; // Fifth column
                const marketPrice = ForceCalc.utils.parseNum(row.querySelector('.market-price').textContent);

                const profit = ForceCalc.tokenShop.calculateProfit(marketPrice, tokenRequired, itemQuantity, tokenType);

                // Update profit display with proper formatting
                const profitCell = row.querySelector('.profit-per-purchase');
                if (profitCell) {
                    profitCell.textContent = ForceCalc.utils.formatWithDots(profit.toFixed(0));
                    ForceCalc.utils.updateProfitStyle(profitCell, profit);
                }
            });
        },

        // Create favorites filter for token shop
        createTokenShopFilters: () => {
            const filterContainer = document.createElement('div');
            filterContainer.id = 'token-shop-filters';
            filterContainer.innerHTML = `
                <label><input type="checkbox" id="token-shop-favorites-filter"> ⭐ Show Only Favorites</label>
            `;

            const placeholder = document.getElementById('token-calculator-placeholder');
            placeholder?.insertBefore(filterContainer, placeholder.firstChild);

            // Add event listener for the filter
            document.getElementById('token-shop-favorites-filter').addEventListener('change', ForceCalc.tokenShop.filterTable);
        },

        // Filter token shop table based on selected filters
        filterTable: () => {
            const showOnlyFavorites = document.getElementById('token-shop-favorites-filter')?.checked || false;

            document.querySelectorAll('.token-shop-table tbody tr').forEach(row => {
                const itemName = row.querySelector('td:nth-child(2)').textContent;
                const show = !showOnlyFavorites || ForceCalc.data.favoriteRecipes.has(itemName);
                row.style.display = show ? '' : 'none';
            });
        },

        // Sort token table by a specific column
        sortTokenTable: (header) => {
            const table = header.closest('table');
            const tbody = table.querySelector('tbody');
            const columnIndex = Array.from(header.parentNode.children).indexOf(header);
            const isAsc = !header.classList.contains('sort-asc');

            // Clear sorting indicators on all headers
            table.querySelectorAll('th').forEach(th => th.classList.remove('sort-asc', 'sort-desc'));

            // Set sorting indicator on current header
            header.classList.add(isAsc ? 'sort-asc' : 'sort-desc');

            // Get all rows as an array for sorting
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Sort the rows based on the profit column
            rows.sort((a, b) => {
                // Get the cell content (we know it's the profit-per-purchase cell)
                const profitCellA = a.querySelector('.profit-per-purchase');
                const profitCellB = b.querySelector('.profit-per-purchase');

                if (!profitCellA || !profitCellB) return 0;

                // Get the text and remove ALL dots for proper numeric comparison
                const aText = profitCellA.textContent.replace(/\./g, '');
                const bText = profitCellB.textContent.replace(/\./g, '');

                // Convert to numbers for comparison
                let aValue = parseFloat(aText);
                let bValue = parseFloat(bText);

                // Handle NaN cases
                if (isNaN(aValue)) aValue = 0;
                if (isNaN(bValue)) bValue = 0;

                // Sort ascending or descending
                return isAsc ? aValue - bValue : bValue - aValue;
            });

            // Reattach rows in the sorted order
            rows.forEach(row => tbody.appendChild(row));
        },

        // Update item prices in token shop table
        updateItemPrices: (itemName, newPrice) => {
            const normalizedItemName = ForceCalc.utils.normalizeItemName(itemName);

            document.querySelectorAll('.token-shop-table .market-price').forEach(priceSpan => {
                const tokenItemName = priceSpan.getAttribute('data-item-name');
                if (ForceCalc.utils.normalizeItemName(tokenItemName) === normalizedItemName) {
                    ForceCalc.utils.setTextPreserveCursor(priceSpan, newPrice.toString());

                    // Update profit calculation
                    const row = priceSpan.closest('tr');
                    const itemQuantity = parseInt(row.querySelector('td:nth-child(3)').textContent) || 1;
                    const tokenType = row.querySelector('td:nth-child(4)').textContent;
                    const tokenRequired = parseInt(row.querySelector('td:nth-child(5)').textContent) || 0;

                    const profit = ForceCalc.tokenShop.calculateProfit(newPrice, tokenRequired, itemQuantity, tokenType);
                    const profitCell = row.querySelector('.profit-per-purchase');
                    profitCell.textContent = ForceCalc.utils.formatWithDots(profit.toFixed(0));
                    ForceCalc.utils.updateProfitStyle(profitCell, profit);
                }
            });
        },

        // Handle market price input in the Devil Shop calculator
        handleMarketPriceInput: (e) => {
            const target = e.target;
            const itemName = target.getAttribute('data-item-name');
            const newPrice = ForceCalc.utils.parseNum(target.textContent);

            // Define normalizedItemName here so it's available for the entire function
            const normalizedItemName = ForceCalc.utils.normalizeItemName(itemName);

            // Use the utility method to set price
            ForceCalc.utils.setMarketPrice(itemName, newPrice);

            // Recalculate profit
            const row = target.closest('tr');
            const itemQuantity = parseInt(row.querySelector('td:nth-child(3)').textContent) || 1;
            const tokenType = row.querySelector('td:nth-child(4)').textContent;
            const tokenRequired = parseInt(row.querySelector('td:nth-child(5)').textContent) || 0;

            const profit = ForceCalc.tokenShop.calculateProfit(newPrice, tokenRequired, itemQuantity, tokenType);
            const profitCell = row.querySelector('.profit-per-purchase');
            profitCell.textContent = ForceCalc.utils.formatWithDots(profit.toFixed(0));
            ForceCalc.utils.updateProfitStyle(profitCell, profit);

            // CRITICAL FIX: If Chloe calculator exists, update prices there too
            if (typeof ForceCalc.crafting?.updateRecipeMarketPrices === 'function') {
                ForceCalc.crafting.updateRecipeMarketPrices(itemName, newPrice);
            }

            // CRITICAL FIX 2: Also directly update any recipe rows with the same name in Chloe calculator
            if (document.querySelector('.crafting-table')) {
                // normalizedItemName is now defined above

                // Look for recipe rows with matching name and update them
                document.querySelectorAll('.crafting-item-row').forEach(recipeRow => {
                    const recipeName = recipeRow.dataset.recipeName;
                    if (recipeName && ForceCalc.utils.normalizeItemName(recipeName) === normalizedItemName) {
                        const marketPriceSpan = recipeRow.querySelector('.market-price');
                        if (marketPriceSpan) {
                            ForceCalc.utils.setTextPreserveCursor(marketPriceSpan, newPrice.toString());
                            // Also recalculate the row
                            if (typeof ForceCalc.crafting?.recalculateRow === 'function') {
                                ForceCalc.crafting.recalculateRow(recipeRow);
                            }
                        }
                    }
                });
            }

            // Check for other items with same normalized name and update them all
            for (const key in ForceCalc.data.ingredientPrices.marketPrices) {
                if (ForceCalc.utils.normalizeItemName(key) === normalizedItemName && key !== itemName) {
                    ForceCalc.data.ingredientPrices.marketPrices[key] = newPrice;
                }
            }

            for (const key in ForceCalc.data.ingredientPrices.ingredients) {
                if (ForceCalc.utils.normalizeItemName(key) === normalizedItemName && key !== itemName) {
                    ForceCalc.data.ingredientPrices.ingredients[key] = newPrice;
                }
            }
        },

        // Create the token shop table
        createTokenShopTable: (tokenShopData) => {
            // Add the control panel (clear button, OCR button, and sales fee)
            const controlPanel = document.createElement('div');
            controlPanel.className = 'calculator-controls';
            controlPanel.appendChild(ForceCalc.ui.createClearPricesButton());
            controlPanel.appendChild(ForceCalc.ui.createOCRPricesButton());
            controlPanel.appendChild(ForceCalc.ui.createSalesFeeInput());

            const placeholder = document.getElementById('token-calculator-placeholder');
            if (placeholder) {
                placeholder.parentNode.insertBefore(controlPanel, placeholder);
            }

            const tableHTML = `
                <div class="token-shop-table-container">
                    <div class="token-shop-table-wrapper">
                        <table class="token-shop-table">
                            <thead>
                                <tr>
                                    <th>⭐</th>
                                    <th>Item Name</th>
                                    <th>Quantity</th>
                                    <th>Token Type</th>
                                    <th>Tokens Required</th>
                                    <th>Market Price (per piece)</th>
                                    <th class="sortable">Profit Per Purchase</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>`;

            if (placeholder) {
                placeholder.insertAdjacentHTML('beforeend', tableHTML);
                const tbody = document.querySelector('.token-shop-table tbody');

                tokenShopData.forEach(item => {
                    const itemName = item.name;
                    const itemQuantity = item.quantity || 1;
                    const tokenType = item.tokenType;
                    const tokenRequired = item.tokensRequired || 0;

                    // Use the market price utility method
                    const savedMarketPrice = ForceCalc.utils.getMarketPrice(itemName) || 0;

                    const profit = ForceCalc.tokenShop.calculateProfit(savedMarketPrice, tokenRequired, itemQuantity, tokenType);

                    const formattedProfit = ForceCalc.utils.formatWithDots(profit.toFixed(0));

                    const tableRow = document.createElement('tr');
                    tableRow.innerHTML = `
                        <td><span class="favorite-btn ${ForceCalc.data.favoriteRecipes.has(itemName) ? 'active' : ''}" data-item="${itemName}">♥</span></td>
                        <td>${itemName}</td>
                        <td>${itemQuantity}</td>
                        <td>${tokenType}</td>
                        <td>${tokenRequired}</td>
                        <td><span contenteditable="true" class="market-price" data-item-name="${itemName}">${savedMarketPrice}</span></td>
                        <td class="profit-per-purchase">${formattedProfit}</td>
                    `;

                    // Add event listeners
                    tableRow.querySelector('.favorite-btn').addEventListener('click', (e) => {
                        ForceCalc.ui.toggleFavorite(e, itemName);
                        ForceCalc.tokenShop.filterTable();
                    });

                    // Update the event listener to use the new method
                    tableRow.querySelector('.market-price').addEventListener('input', ForceCalc.tokenShop.handleMarketPriceInput);

                    tbody.appendChild(tableRow);

                    // Apply initial profit styling
                    ForceCalc.utils.updateProfitStyle(tableRow.querySelector('.profit-per-purchase'), profit);
                });

                // Add sorting capability
                document.querySelector('.token-shop-table th.sortable').addEventListener('click', (e) => {
                    ForceCalc.tokenShop.sortTokenTable(e.target);
                });
            }
        },

        // Initialize the Devil Shop calculator
        init: () => {
            ForceCalc.tokenShop.loadTokenPrices();

            // Check if DevilShopData is available (should be pre-loaded via wp_enqueue_script)
            if (typeof DevilShopData !== 'undefined' && DevilShopData.items) {
                ForceCalc.tokenShop.createTokenPriceConfig();
                ForceCalc.tokenShop.createTokenShopFilters();
                ForceCalc.tokenShop.createTokenShopTable(DevilShopData.items);
            } else {
                console.error("Devil Shop data is not available.");
                const placeholder = document.getElementById('token-calculator-placeholder');
                if (placeholder) {
                    placeholder.innerHTML = '<p class="error-message">Failed to load token shop data. Please check if the data file is correctly loaded.</p>';
                }
            }
        }
    };

    // Initialize the Devil Shop calculator
    ForceCalc.tokenShop.init();
});